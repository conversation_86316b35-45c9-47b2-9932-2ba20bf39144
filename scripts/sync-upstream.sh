#!/bin/bash
# Script para sincronizar com upstream Polar de forma segura
# Uso: ./scripts/sync-upstream.sh

set -e  # Parar em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Função para imprimir mensagens
info() {
    echo -e "${GREEN}ℹ️  $1${NC}"
}

warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar se estamos em um repositório git
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    error "Não é um repositório git!"
    exit 1
fi

# Verificar se o remote 'polar' existe
if ! git remote | grep -q "^polar$"; then
    error "Remote 'polar' não encontrado!"
    info "Adicione com: git remote add polar https://github.com/polarsource/polar.git"
    exit 1
fi

# Verificar se há mudanças não commitadas
if ! git diff-index --quiet HEAD --; then
    error "Há mudanças não commitadas!"
    info "Por favor, commit ou stash suas mudanças antes de continuar"
    exit 1
fi

# Data para nome da branch
DATE=$(date +%Y-%m-%d)
BRANCH="sync/upstream-${DATE}"
CURRENT_BRANCH=$(git branch --show-current)

info "🔄 Iniciando sincronização com upstream Polar..."
info "Branch atual: ${CURRENT_BRANCH}"
info "Nova branch de sync: ${BRANCH}"

# Perguntar confirmação
read -p "Continuar? (y/N) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    info "Cancelado pelo usuário"
    exit 0
fi

# 1. Atualizar remotes
info "📥 Buscando atualizações do upstream..."
git fetch polar

# Verificar se há atualizações
LOCAL=$(git rev-parse main)
REMOTE=$(git rev-parse polar/main)

if [ "$LOCAL" = "$REMOTE" ]; then
    info "✅ Já está atualizado com upstream!"
    exit 0
fi

# Mostrar commits novos
info "📋 Novos commits do upstream:"
git log main..polar/main --oneline | head -10

# 2. Criar branch de sync
info "🌿 Criando branch de sync: ${BRANCH}"
git checkout -b "$BRANCH" 2>/dev/null || {
    warn "Branch ${BRANCH} já existe, usando existente"
    git checkout "$BRANCH"
}

# 3. Merge upstream
info "🔀 Fazendo merge do upstream..."
if git merge polar/main --no-ff -m "Sync upstream: ${DATE}"; then
    info "✅ Merge concluído sem conflitos!"
else
    warn "⚠️  Conflitos detectados!"
    info "📝 Resolva os conflitos e depois execute:"
    info "   git add <arquivos-resolvidos>"
    info "   git merge --continue"
    info ""
    info "Arquivos em conflito:"
    git diff --name-only --diff-filter=U
    exit 1
fi

# 4. Verificar customizações (se script existir)
if [ -f "scripts/verify-customizations.sh" ]; then
    info "🔍 Verificando customizações..."
    if ./scripts/verify-customizations.sh; then
        info "✅ Customizações verificadas"
    else
        warn "⚠️  Algumas customizações podem ter sido afetadas"
    fi
fi

# 5. Resumo
info ""
info "✅ Sync concluído!"
info ""
info "📝 Próximos passos:"
info "   1. Revise as mudanças: git log main..${BRANCH}"
info "   2. Teste o código (especialmente payment providers)"
info "   3. Se tudo estiver OK, faça merge na main:"
info "      git checkout main"
info "      git merge ${BRANCH} --no-ff"
info "      git push origin main"
info ""
info "Branch de sync: ${BRANCH}"


