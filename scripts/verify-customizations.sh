#!/bin/bash
# Script para verificar que customizações não foram perdidas após merge
# Uso: ./scripts/verify-customizations.sh

set -e

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

error() {
    echo -e "${RED}❌ $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

info() {
    echo "ℹ️  $1"
}

ERRORS=0

info "🔍 Verificando customizações..."

# 1. Verificar PaymentProcessor.pagarme
info "Verificando PaymentProcessor.pagarme..."
if grep -q "pagarme.*=" server/polar/enums.py 2>/dev/null; then
    success "PaymentProcessor.pagarme encontrado"
else
    error "PaymentProcessor.pagarme NÃO encontrado em server/polar/enums.py"
    ERRORS=$((ERRORS + 1))
fi

# 2. Verificar PagarmeProvider registrado
info "Verificando registro do PagarmeProvider..."
if grep -q "PagarmeProvider\|pagarme.*provider" server/polar/integrations/payment_providers/__init__.py 2>/dev/null; then
    success "PagarmeProvider registrado"
else
    warn "PagarmeProvider pode não estar registrado (verificar manualmente)"
fi

# 3. Verificar estrutura do diretório pagarme
info "Verificando estrutura do provider Pagarme..."
if [ -d "server/polar/integrations/payment_providers/pagarme" ]; then
    success "Diretório pagarme existe"
    
    # Verificar arquivos principais
    if [ -f "server/polar/integrations/payment_providers/pagarme/provider.py" ]; then
        success "  - provider.py existe"
    else
        error "  - provider.py NÃO encontrado"
        ERRORS=$((ERRORS + 1))
    fi
    
    if [ -f "server/polar/integrations/payment_providers/pagarme/webhooks.py" ]; then
        success "  - webhooks.py existe"
    else
        warn "  - webhooks.py não encontrado (pode ser opcional)"
    fi
else
    error "Diretório pagarme NÃO encontrado"
    ERRORS=$((ERRORS + 1))
fi

# 4. Verificar PaymentProviderRegistry
info "Verificando PaymentProviderRegistry..."
if grep -q "PaymentProviderRegistry" server/polar/integrations/payment_providers/registry.py 2>/dev/null; then
    success "PaymentProviderRegistry encontrado"
else
    error "PaymentProviderRegistry NÃO encontrado"
    ERRORS=$((ERRORS + 1))
fi

# 5. Verificar que clients/ existe (frontend customizado)
info "Verificando frontend customizado..."
if [ -d "clients" ]; then
    success "Diretório clients/ existe"
else
    warn "Diretório clients/ não encontrado"
fi

# 6. Verificar documentação de customizações
info "Verificando documentação..."
if [ -f "_docs/FORK_SYNC_STRATEGY.md" ]; then
    success "Documentação de sync encontrada"
else
    warn "Documentação de sync não encontrada"
fi

# Resumo
echo ""
if [ $ERRORS -eq 0 ]; then
    success "✅ Todas as verificações passaram!"
    exit 0
else
    error "❌ $ERRORS erro(s) encontrado(s)"
    info "Por favor, revise os erros acima antes de fazer merge na main"
    exit 1
fi


