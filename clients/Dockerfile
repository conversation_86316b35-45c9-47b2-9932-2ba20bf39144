# Dockerfile para Frontend Next.js - Polar
# Este Dockerfile deve ser executado a partir da raiz do monorepo
# Atualizado: 2024-11-06 - Configu<PERSON> para Google Cloud Run
FROM node:22-slim AS base

# Instalar pnpm
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

# Dependências do sistema
RUN apt-get update && apt-get install -y \
    libc6 \
    build-essential \
    python3 \
    && rm -rf /var/lib/apt/lists/*

# Stage 1: Build completo (deps + build)
FROM base AS builder
WORKDIR /app

# Build args para variáveis de ambiente NEXT_PUBLIC_*
# Essas variáveis precisam estar disponíveis durante o build do Next.js
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_FRONTEND_BASE_URL
ARG NEXT_PUBLIC_ENVIRONMENT
ARG NEXT_PUBLIC_VERCEL_ENV
ARG NEXT_PUBLIC_LOGIN_PATH
ARG NEXT_PUBLIC_GITHUB_APP_NAMESPACE
ARG NEXT_PUBLIC_GITHUB_BADGE_EMBED_DEFAULT_LABEL
ARG NEXT_PUBLIC_SENTRY_DSN
ARG NEXT_PUBLIC_POSTHOG_TOKEN
ARG NEXT_PUBLIC_STRIPE_KEY
ARG NEXT_PUBLIC_CHECKOUT_EMBED_SCRIPT_SRC
ARG NEXT_PUBLIC_APPLE_DOMAIN_ASSOCIATION
ARG NEXT_PUBLIC_GOOGLE_ANALYTICS_ID
ARG NEXT_PUBLIC_CHECKOUT_EXTERNAL_WEBHOOKS_WAITING_LIMIT_MS

# Passar build args como variáveis de ambiente para o build do Next.js
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_FRONTEND_BASE_URL=${NEXT_PUBLIC_FRONTEND_BASE_URL}
ENV NEXT_PUBLIC_ENVIRONMENT=${NEXT_PUBLIC_ENVIRONMENT}
ENV NEXT_PUBLIC_VERCEL_ENV=${NEXT_PUBLIC_VERCEL_ENV}
ENV NEXT_PUBLIC_LOGIN_PATH=${NEXT_PUBLIC_LOGIN_PATH}
ENV NEXT_PUBLIC_GITHUB_APP_NAMESPACE=${NEXT_PUBLIC_GITHUB_APP_NAMESPACE}
ENV NEXT_PUBLIC_GITHUB_BADGE_EMBED_DEFAULT_LABEL=${NEXT_PUBLIC_GITHUB_BADGE_EMBED_DEFAULT_LABEL}
ENV NEXT_PUBLIC_SENTRY_DSN=${NEXT_PUBLIC_SENTRY_DSN}
ENV NEXT_PUBLIC_POSTHOG_TOKEN=${NEXT_PUBLIC_POSTHOG_TOKEN}
ENV NEXT_PUBLIC_STRIPE_KEY=${NEXT_PUBLIC_STRIPE_KEY}
ENV NEXT_PUBLIC_CHECKOUT_EMBED_SCRIPT_SRC=${NEXT_PUBLIC_CHECKOUT_EMBED_SCRIPT_SRC}
ENV NEXT_PUBLIC_APPLE_DOMAIN_ASSOCIATION=${NEXT_PUBLIC_APPLE_DOMAIN_ASSOCIATION}
ENV NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=${NEXT_PUBLIC_GOOGLE_ANALYTICS_ID}
ENV NEXT_PUBLIC_CHECKOUT_EXTERNAL_WEBHOOKS_WAITING_LIMIT_MS=${NEXT_PUBLIC_CHECKOUT_EXTERNAL_WEBHOOKS_WAITING_LIMIT_MS}

# Copiar arquivos de dependências do monorepo
COPY clients/pnpm-lock.yaml ./clients/
COPY clients/package.json ./clients/
COPY clients/pnpm-workspace.yaml ./clients/
COPY clients/turbo.json ./clients/

# Copiar package.json dos packages e apps (necessário para workspace)
# É importante copiar o package.json do app web ANTES de instalar para que optionalDependencies sejam instaladas
COPY clients/packages ./clients/packages
COPY clients/apps/web/package.json ./clients/apps/web/package.json

# Instalar dependências incluindo opcionais (necessário para binários nativos como lightningcss)
# O lightningcss requer o binário nativo lightningcss-linux-x64-gnu que está declarado como optionalDependency
# no package.json do app web. O pnpm instala opcionais por padrão quando a plataforma corresponde.
WORKDIR /app/clients
# Instalar dependências (sem --frozen-lockfile para permitir instalação de opcionais específicas da plataforma Linux)
# O pnpm precisa ver o package.json do app web para instalar as optionalDependencies
RUN echo "===========================================" && \
    echo "📦 ETAPA 1: Instalando dependências com pnpm..." && \
    echo "===========================================" && \
    pnpm install --reporter=append-only && \
    echo "" && \
    echo "===========================================" && \
    echo "📦 ETAPA 2: Instalando dependência opcional lightningcss..." && \
    echo "===========================================" && \
    pnpm -C apps/web add lightningcss-linux-x64-gnu@1.30.2 --save-optional --no-frozen-lockfile || true && \
    echo "" && \
    echo "===========================================" && \
    echo "📦 ETAPA 3: Reinstalando dependências..." && \
    echo "===========================================" && \
    pnpm install --reporter=append-only && \
    echo "" && \
    echo "✅ Dependências instaladas com sucesso!" && \
    echo "📊 Verificando instalação..." && \
    pnpm list --depth=0 | head -20

# Copiar código fonte completo
WORKDIR /app
COPY clients/ ./clients/

# Build da aplicação Next.js usando Turbo
# As variáveis NEXT_PUBLIC_* estão disponíveis como ENV acima
WORKDIR /app/clients
RUN echo "" && \
    echo "===========================================" && \
    echo "🔨 ETAPA 4: Iniciando build do Next.js..." && \
    echo "===========================================" && \
    echo "" && \
    echo "📋 Variáveis de ambiente NEXT_PUBLIC_* configuradas:" && \
    env | grep NEXT_PUBLIC_ | sort && \
    echo "" && \
    echo "🚀 Executando build do Next.js..." && \
    echo "⏳ Isso pode levar vários minutos..." && \
    echo "" && \
    echo "Opção 1: Tentando com turbo diretamente..." && \
    if pnpm exec turbo run build --filter=web; then \
      echo "✅ Build com turbo concluído com sucesso!"; \
    else \
      echo "" && \
      echo "⚠️  Build com turbo falhou, tentando opção 2..." && \
      echo "Opção 2: Tentando com pnpm build direto no app..." && \
      cd apps/web && \
      if pnpm run build; then \
        echo "✅ Build direto concluído com sucesso!"; \
        cd ../..; \
      else \
        echo "❌ ERRO: Build do Next.js falhou em todas as tentativas!" && \
        echo "Verificando logs acima para mais detalhes..." && \
        cd ../..; \
        exit 1; \
      fi; \
    fi && \
    echo "" && \
    echo "===========================================" && \
    echo "✅ Build do Next.js concluído com sucesso!" && \
    echo "===========================================" && \
    echo "" && \
    echo "📁 Verificando diretório .next..." && \
    if [ ! -d "apps/web/.next" ]; then \
      echo "❌ ERRO CRÍTICO: Diretório .next não foi gerado!" && \
      echo "Conteúdo de apps/web:" && \
      ls -la apps/web/ && \
      exit 1; \
    fi && \
    ls -lah apps/web/.next | head -10 && \
    echo "" && \
    echo "📊 Tamanho do diretório .next:" && \
    du -sh apps/web/.next && \
    echo "" && \
    echo "✅ Build finalizado e validado!"

# Stage 3: Runtime
FROM base AS runner
WORKDIR /app

# Garantir que pnpm está disponível
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Configurar corepack para não precisar de cache (já que pnpm está instalado)
ENV COREPACK_ENABLE_STRICT=0

# Criar usuário não-root
RUN groupadd --system --gid 1001 nodejs && \
    useradd --system --uid 1001 nextjs

# Criar diretório de cache do corepack para o usuário nextjs
# Isso evita erro de permissão quando o corepack tentar criar o diretório
RUN mkdir -p /home/<USER>/.cache/node/corepack && \
    chown -R nextjs:nodejs /home/<USER>/.cache

# Copiar estrutura completa do monorepo mantendo os caminhos corretos
# Copiar node_modules primeiro (necessário para o Next.js)
COPY --from=builder --chown=nextjs:nodejs /app/clients/node_modules ./clients/node_modules

# Copiar workspace packages (necessário para workspace dependencies)
COPY --from=builder --chown=nextjs:nodejs /app/clients/packages ./clients/packages

# Copiar app web completo (inclui .next, public, package.json)
COPY --from=builder --chown=nextjs:nodejs /app/clients/apps/web ./clients/apps/web

# O Cloud Run injeta automaticamente a variável PORT
# Não definimos PORT aqui porque o Cloud Run injeta essa variável automaticamente
# O Next.js precisa escutar na porta que o Cloud Run fornece
ENV HOSTNAME="0.0.0.0"

# Expor a porta padrão do Cloud Run (8080) - mas o container usará a variável PORT injetada
EXPOSE 8080

# Trabalhar a partir do diretório do workspace para que pnpm encontre as dependências
WORKDIR /app/clients

# Criar script de startup antes de mudar o usuário
# Garantir que PATH inclui pnpm e node
RUN echo '#!/bin/sh\n\
set -e\n\
export PATH="/pnpm:$PATH"\n\
export PORT=${PORT:-8080}\n\
echo "==========================================="\n\
echo "Starting Next.js Application"\n\
echo "PORT: $PORT"\n\
echo "NODE_ENV: $NODE_ENV"\n\
echo "PATH: $PATH"\n\
echo "Working directory: $(pwd)"\n\
echo ""\n\
echo "Checking Node.js..."\n\
node --version || (echo "ERROR: Node.js not found!" && exit 1)\n\
echo ""\n\
echo "Checking pnpm..."\n\
which pnpm || (echo "ERROR: pnpm not found in PATH!" && echo "Trying /usr/local/bin/pnpm..." && export PATH="/usr/local/bin:$PATH" && which pnpm || exit 1)\n\
pnpm --version || exit 1\n\
echo ""\n\
cd apps/web\n\
echo "Changed to: $(pwd)"\n\
echo ""\n\
if [ ! -d ".next" ]; then\n\
  echo "ERROR: .next directory not found!"\n\
  echo "Contents of apps/web:"\n\
  ls -la\n\
  exit 1\n\
fi\n\
echo "✓ .next directory found"\n\
echo ""\n\
echo "Checking Next.js..."\n\
pnpm exec next --version || (echo "ERROR: Next.js not found!" && echo "Trying to find next..." && find ../../node_modules -name "next" -type f 2>/dev/null | head -3 && exit 1)\n\
echo ""\n\
echo "Starting Next.js on 0.0.0.0:$PORT..."\n\
exec pnpm exec next start -H 0.0.0.0 -p $PORT\n\
' > /app/clients/start.sh && chmod +x /app/clients/start.sh && chown nextjs:nodejs /app/clients/start.sh

USER nextjs

# Usar o script de startup
CMD ["/app/clients/start.sh"]

