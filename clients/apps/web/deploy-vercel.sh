#!/bin/bash

# Script de deploy do frontend Next.js na Vercel
# Apontando para backend no Google Cloud Run

set -e

# Cores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Deploy do Frontend Polar na Vercel${NC}\n"

# URL do backend no Google Cloud Run
BACKEND_URL="https://polar-api-iuu5qv6jja-rj.a.run.app"

echo -e "${YELLOW}📋 Configurações:${NC}"
echo -e "  Backend URL: ${BACKEND_URL}"
echo -e "  Diretório: clients/apps/web"
echo ""

# Verificar se Vercel CLI está instalado
if ! command -v vercel &> /dev/null; then
    echo -e "${RED}❌ Vercel CLI não encontrado!${NC}"
    echo -e "${YELLOW}Instale com: npm i -g vercel${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Vercel CLI encontrado${NC}\n"

# Navegar para o diretório do app web
cd "$(dirname "$0")"

echo -e "${YELLOW}📦 Configurando variáveis de ambiente...${NC}\n"

# Configurar variáveis de ambiente essenciais
vercel env add NEXT_PUBLIC_API_URL production <<< "$BACKEND_URL" 2>/dev/null || \
    vercel env rm NEXT_PUBLIC_API_URL production -y 2>/dev/null && \
    vercel env add NEXT_PUBLIC_API_URL production <<< "$BACKEND_URL"

# Variáveis básicas
vercel env add NODE_ENV production <<< "production" 2>/dev/null || true
vercel env add NEXT_PUBLIC_ENVIRONMENT production <<< "production" 2>/dev/null || true
vercel env add NEXT_TELEMETRY_DISABLED production <<< "1" 2>/dev/null || true

# Variáveis de autenticação
vercel env add POLAR_AUTH_COOKIE_KEY production <<< "fluu_session" 2>/dev/null || true
vercel env add POLAR_AUTH_MCP_COOKIE_KEY production <<< "fluu_mcp_session" 2>/dev/null || true
vercel env add NEXT_PUBLIC_LOGIN_PATH production <<< "/login" 2>/dev/null || true

# GitHub
vercel env add NEXT_PUBLIC_GITHUB_APP_NAMESPACE production <<< "polar-sh" 2>/dev/null || true
vercel env add NEXT_PUBLIC_GITHUB_BADGE_EMBED_DEFAULT_LABEL production <<< "Fund" 2>/dev/null || true

echo -e "\n${GREEN}✅ Variáveis de ambiente configuradas${NC}\n"

echo -e "${YELLOW}📝 Variáveis opcionais que você pode configurar manualmente:${NC}"
echo -e "  - NEXT_PUBLIC_SENTRY_DSN (monitoramento)"
echo -e "  - NEXT_PUBLIC_POSTHOG_TOKEN (analytics)"
echo -e "  - NEXT_PUBLIC_STRIPE_KEY (pagamentos)"
echo -e "  - S3_PUBLIC_IMAGES_BUCKET_HOSTNAME (imagens)"
echo ""

# Perguntar se deseja fazer deploy
read -p "Deseja fazer deploy agora? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}⚠️  Deploy cancelado. Execute 'vercel --prod' quando estiver pronto.${NC}"
    exit 0
fi

echo -e "\n${GREEN}🚀 Iniciando deploy...${NC}\n"

# Fazer deploy de produção
vercel --prod

echo -e "\n${GREEN}✅ Deploy concluído!${NC}"
echo -e "${YELLOW}📝 Não esqueça de:${NC}"
echo -e "  1. Atualizar NEXT_PUBLIC_FRONTEND_BASE_URL com a URL da Vercel"
echo -e "  2. Configurar CORS no backend do Google Cloud para aceitar a nova URL"
echo -e "  3. Configurar variáveis opcionais se necessário"
echo ""

