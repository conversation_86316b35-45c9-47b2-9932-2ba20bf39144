{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3000 --turbopack", "build": "next build --turbopack", "start": "next start", "format": "prettier --write ./src", "format:check": "prettier -c ./src", "lint:only": "eslint .", "lint": "pnpm format:check && pnpm lint:only", "lint:changed": "eslint $(git diff --name-only --relative --diff-filter=ACMR HEAD | grep -E '\\.(js|jsx|ts|tsx)$')", "test": "vitest run", "typecheck": "tsc --noEmit", "analyze": "ANALYZE=true next build"}, "engines": {"node": "=22"}, "optionalDependencies": {"lightningcss-linux-x64-gnu": "1.30.2"}, "dependencies": {"@ai-sdk/anthropic": "^2.0.39", "@ai-sdk/google": "^2.0.25", "@ai-sdk/mcp": "^0.0.4", "@ai-sdk/react": "^2.0.84", "@date-fns/utc": "^2.1.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/error-message": "^2.0.1", "@mdx-js/loader": "^3.1.1", "@mdx-js/react": "^3.1.1", "@modelcontextprotocol/sdk": "^1.20.2", "@mui/icons-material": "^7.3.4", "@mui/material": "^7.3.4", "@next/mdx": "16.0.1", "@next/third-parties": "16.0.1", "@polar-sh/checkout": "workspace:^", "@polar-sh/client": "workspace:*", "@polar-sh/mdx": "workspace:*", "@polar-sh/sdk": "^0.40.2", "@polar-sh/ui": "workspace:*", "@posthog/ai": "^6.5.0", "@posthog/core": "^1.4.0", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-toast": "^1.2.15", "@sentry/nextjs": "^10.22.0", "@shikijs/rehype": "^3.14.0", "@stripe/react-stripe-js": "^4.0.2", "@stripe/stripe-js": "^7.9.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.16", "@tailwindcss/typography": "^0.5.19", "@tanstack/react-query": "^5.90.5", "@tanstack/react-table": "^8.21.3", "@vercel/blob": "^0.23.4", "ai": "^5.0.84", "big.js": "^7.0.1", "class-variance-authority": "^0.7.1", "crawler-user-agents": "^1.21.0", "date-fns": "^4.1.0", "dom-to-image": "^2.6.0", "event-source-plus": "^0.1.12", "eventemitter3": "^5.0.1", "framer-motion": "^12.23.24", "geist": "^1.5.1", "import-in-the-middle": "^1.14.4", "lodash.debounce": "^4.0.8", "lucide-react": "^0.552.0", "markdown-to-jsx": "^7.7.17", "nanoid": "^5.1.6", "next": "16.0.1", "next-themes": "^0.4.6", "nuqs": "^2.7.2", "posthog-js": "^1.282.0", "posthog-node": "^5.10.4", "qrcode": "^1.5.4", "react": "19.2.0", "react-dom": "19.2.0", "react-dropzone": "^14.3.8", "react-focus-lock": "^2.13.6", "react-hook-form": "^7.65.0", "react-timeago": "^8.3.0", "react-use": "^17.6.0", "rehype-mdx-import-media": "^1.2.0", "rehype-slug": "^6.0.0", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "require-in-the-middle": "^7.5.2", "shiki": "^3.14.0", "slugify": "^1.6.6", "tailwind-merge": "^3.3.1", "tailwindcss-radix": "^4.0.2", "tinycolor2": "^1.6.0", "tw-animate-css": "^1.4.0", "zod": "^4.1.12"}, "devDependencies": {"@next/bundle-analyzer": "16.0.1", "@polar-sh/eslint-config": "workspace:*", "@polar-sh/typescript-config": "workspace:*", "@types/big.js": "^6.2.2", "@types/d3": "^7.4.3", "@types/dom-to-image": "^2.6.7", "@types/lodash.debounce": "^4.0.9", "@types/lunr": "^2.3.7", "@types/mdx": "^2.0.13", "@types/node": "^22.18.0", "@types/qrcode": "^1.5.6", "@types/react": "19.2.2", "@types/react-dom": "19.2.2", "@types/tinycolor2": "^1.4.6", "@types/web": "^0.0.286", "@vitejs/plugin-react": "^5.1.0", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.38.0", "gzip-size": "^7.0.0", "jsdom": "^27.1.0", "mkdirp": "^3.0.1", "postcss": "^8.5.6", "prop-types": "^15.8.1", "remark": "^15.0.1", "remark-mdx": "^3.1.1", "tailwindcss": "^4.1.16", "ts-node": "^10.9.2", "typescript": "5.9.3", "unist-util-visit": "^5.0.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "nextBundleAnalysis": {"budget": 358400, "budgetPercentIncreaseRed": 20, "minimumChangeThreshold": 100, "showDetails": true}, "pnpm": {"overrides": {"@types/react": "19.2.2", "@types/react-dom": "19.2.2"}}}