#!/bin/bash

# Script para corrigir deploy do Vercel em monorepo

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${GREEN}🔧 Corrigindo configuração do Vercel para monorepo${NC}\n"

cd "$(dirname "$0")"

# Verificar se Next.js está no package.json
if ! grep -q '"next"' package.json; then
    echo -e "${RED}❌ Next.js não encontrado no package.json${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Next.js encontrado no package.json${NC}"

# Verificar vercel.json
if [ ! -f "vercel.json" ]; then
    echo -e "${RED}❌ vercel.json não encontrado${NC}"
    exit 1
fi

echo -e "${GREEN}✅ vercel.json encontrado${NC}\n"

echo -e "${YELLOW}📋 Configuração atual do vercel.json:${NC}"
cat vercel.json | grep -E "(installCommand|buildCommand|framework)" || true
echo ""

# Verificar se está linkado
if [ ! -f ".vercel/project.json" ]; then
    echo -e "${YELLOW}⚠️  Projeto não está linkado. Fazendo link...${NC}"
    vercel link --yes
else
    echo -e "${GREEN}✅ Projeto já está linkado${NC}"
    PROJECT_ID=$(cat .vercel/project.json | grep -o '"projectId":"[^"]*' | cut -d'"' -f4)
    echo -e "${BLUE}Project ID: ${PROJECT_ID}${NC}\n"
fi

# Calcular caminho relativo à raiz do repositório git
GIT_ROOT=$(git rev-parse --show-toplevel 2>/dev/null || echo "")
if [ -n "$GIT_ROOT" ]; then
    # Usar Python para calcular caminho relativo (cross-platform)
    RELATIVE_PATH=$(python3 -c "import os; print(os.path.relpath('$PWD', '$GIT_ROOT'))" 2>/dev/null || echo "clients/apps/web")
else
    RELATIVE_PATH="clients/apps/web"
fi

echo -e "${YELLOW}📝 IMPORTANTE: Configure o Root Directory no dashboard da Vercel:${NC}"
echo -e "${BLUE}1. Acesse: https://vercel.com/ismael-costas-projects/web/settings${NC}"
echo -e "${BLUE}2. Vá em Settings → General${NC}"
echo -e "${BLUE}3. Configure Root Directory como: ${RELATIVE_PATH}${NC}"
echo -e "${BLUE}   (caminho relativo à raiz do repositório git)${NC}"
echo ""

read -p "Já configurou o Root Directory? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}⚠️  Configure o Root Directory antes de fazer deploy${NC}"
    echo -e "${BLUE}Acesse: https://vercel.com/ismael-costas-projects/web/settings${NC}"
    exit 0
fi

echo -e "\n${GREEN}🚀 Fazendo deploy de produção...${NC}\n"

vercel --prod

echo -e "\n${GREEN}✅ Deploy concluído!${NC}"

