#!/bin/bash

# Script completo de deploy do frontend Next.js na Vercel
# Apontando para backend no Google Cloud Run

set -e

# Cores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Deploy do Frontend Polar na Vercel${NC}\n"

# URL do backend no Google Cloud Run
BACKEND_URL="https://polar-api-iuu5qv6jja-rj.a.run.app"

echo -e "${YELLOW}📋 Configurações:${NC}"
echo -e "  Backend URL: ${BACKEND_URL}"
echo -e "  Diretório: clients/apps/web"
echo ""

# Verificar se Vercel CLI está instalado
if ! command -v vercel &> /dev/null; then
    echo -e "${RED}❌ Vercel CLI não encontrado!${NC}"
    echo -e "${YELLOW}Instale com: npm i -g vercel${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Vercel CLI encontrado${NC}\n"

# Navegar para o diretório do app web
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$SCRIPT_DIR"

# Verificar se está linkado
if ! vercel project ls &> /dev/null; then
    echo -e "${YELLOW}⚠️  Você precisa estar autenticado na Vercel${NC}"
    echo -e "${BLUE}Execute: vercel login${NC}"
    exit 1
fi

# Verificar link do projeto
if [ ! -f ".vercel/project.json" ]; then
    echo -e "${YELLOW}📎 Projeto não está linkado. Criando link...${NC}"
    echo -e "${BLUE}Selecione as opções quando solicitado:${NC}"
    echo -e "  - Escolha um projeto existente ou crie um novo"
    echo -e "  - Diretório: $(pwd)"
    echo ""
    
    vercel link --yes || {
        echo -e "${RED}❌ Falha ao linkar projeto${NC}"
        exit 1
    }
else
    echo -e "${GREEN}✅ Projeto já está linkado${NC}\n"
fi

# Ler informações do projeto
if [ -f ".vercel/project.json" ]; then
    PROJECT_NAME=$(cat .vercel/project.json | grep -o '"name":"[^"]*' | cut -d'"' -f4 || echo "polar-frontend")
    echo -e "${BLUE}Projeto: ${PROJECT_NAME}${NC}\n"
fi

echo -e "${YELLOW}📦 Configurando variáveis de ambiente...${NC}\n"

# Função para adicionar variável de ambiente
add_env_var() {
    local var_name=$1
    local var_value=$2
    local scope=${3:-production}
    
    echo -e "${BLUE}Configurando ${var_name}...${NC}"
    
    # Tentar adicionar (pode falhar se já existir)
    echo "$var_value" | vercel env add "$var_name" "$scope" 2>/dev/null || {
        # Se já existe, perguntar se quer atualizar
        read -p "  ${var_name} já existe. Atualizar? (y/n) " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            vercel env rm "$var_name" "$scope" -y 2>/dev/null || true
            echo "$var_value" | vercel env add "$var_name" "$scope"
        fi
    }
}

# Variáveis obrigatórias
add_env_var "NEXT_PUBLIC_API_URL" "$BACKEND_URL" "production"
add_env_var "NODE_ENV" "production" "production"
add_env_var "NEXT_PUBLIC_ENVIRONMENT" "production" "production"
add_env_var "NEXT_TELEMETRY_DISABLED" "1" "production"

# Variáveis de autenticação
add_env_var "POLAR_AUTH_COOKIE_KEY" "fluu_session" "production"
add_env_var "POLAR_AUTH_MCP_COOKIE_KEY" "fluu_mcp_session" "production"
add_env_var "NEXT_PUBLIC_LOGIN_PATH" "/login" "production"

# GitHub
add_env_var "NEXT_PUBLIC_GITHUB_APP_NAMESPACE" "polar-sh" "production"
add_env_var "NEXT_PUBLIC_GITHUB_BADGE_EMBED_DEFAULT_LABEL" "Fund" "production"

echo -e "\n${GREEN}✅ Variáveis de ambiente configuradas${NC}\n"

echo -e "${YELLOW}📝 Variáveis opcionais que você pode configurar depois:${NC}"
echo -e "  - NEXT_PUBLIC_SENTRY_DSN (monitoramento)"
echo -e "  - NEXT_PUBLIC_POSTHOG_TOKEN (analytics)"
echo -e "  - NEXT_PUBLIC_STRIPE_KEY (pagamentos)"
echo -e "  - S3_PUBLIC_IMAGES_BUCKET_HOSTNAME (imagens)"
echo ""

# Perguntar se deseja fazer deploy
read -p "Deseja fazer deploy agora? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}⚠️  Deploy cancelado. Execute 'vercel --prod' quando estiver pronto.${NC}"
    exit 0
fi

echo -e "\n${GREEN}🚀 Iniciando deploy de produção...${NC}\n"

# Fazer deploy de produção
vercel --prod --yes

# Obter URL do deploy
DEPLOY_URL=$(vercel ls --prod | grep -m 1 "polar" | awk '{print $2}' || echo "")

if [ -n "$DEPLOY_URL" ]; then
    echo -e "\n${GREEN}✅ Deploy concluído!${NC}"
    echo -e "${GREEN}🌐 URL: ${DEPLOY_URL}${NC}\n"
    
    echo -e "${YELLOW}📝 Próximos passos:${NC}"
    echo -e "  1. Atualizar NEXT_PUBLIC_FRONTEND_BASE_URL:"
    echo -e "     ${BLUE}vercel env add NEXT_PUBLIC_FRONTEND_BASE_URL production${NC}"
    echo -e "     Digite: ${DEPLOY_URL}"
    echo ""
    echo -e "  2. Configurar CORS no backend do Google Cloud Run:"
    echo -e "     ${BLUE}Atualizar POLAR_FRONTEND_BASE_URL (fluu.digital) e POLAR_CORS_ORIGINS${NC}"
    echo ""
    echo -e "  3. Fazer novo deploy após atualizar variáveis:"
    echo -e "     ${BLUE}vercel --prod${NC}"
else
    echo -e "\n${YELLOW}⚠️  Deploy realizado, mas não foi possível obter a URL automaticamente.${NC}"
    echo -e "${BLUE}Execute 'vercel ls' para ver as URLs disponíveis.${NC}"
fi

echo ""

