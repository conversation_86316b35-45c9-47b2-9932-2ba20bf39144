'use client'

import Button from '@polar-sh/ui/components/atoms/Button'

export default function Error({ error }: { error: Error }) {
  return (
    <div className="flex min-h-screen grow flex-col items-center justify-center space-y-4 bg-blue-100 p-16">
      <h2 className="text-xl">Something went wrong!</h2>

      <Button
        fullWidth={false}
        onClick={() => {
          window.location.href = '/'
        }}
      >
        <span>Go back to Polar</span>
      </Button>

      <p className="pt-24 text-gray-400"></p>

      <pre className="text-sm whitespace-break-spaces text-gray-400">
        Error digest: {'digest' in error ? <>{error.digest}</> : 'oh oh'}
      </pre>
    </div>
  )
}
