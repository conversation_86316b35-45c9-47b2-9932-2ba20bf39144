import LogoIcon from '@/components/Brand/LogoIcon'
import { Metadata } from 'next'
import { redirect } from 'next/navigation'
import ClientPage from './ClientPage'

export const metadata: Metadata = {
  title: 'Enter verification code',
}

export default async function Page(props: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  let searchParams
  try {
    searchParams = await props.searchParams
  } catch (error) {
    // If there's an error reading searchParams, redirect to login
    redirect('/login')
  }
  
  // Properly handle email which can be string or string[]
  const emailParam = searchParams?.email
  const email = Array.isArray(emailParam) ? emailParam[0] : emailParam
  
  // Properly handle return_to which can be string or string[]
  const returnToParam = searchParams?.return_to
  const return_to = Array.isArray(returnToParam) ? returnToParam[0] : returnToParam
  
  // Properly handle error which can be string or string[]
  const errorParam = searchParams?.error
  const error = Array.isArray(errorParam) ? errorParam[0] : errorParam

  // Redirect if email is missing
  if (!email) {
    redirect('/login')
  }

  return (
    <div className="dark:bg-polar-950 flex h-screen w-full grow items-center justify-center bg-gray-50">
      <div id="polar-bg-gradient"></div>
      <div className="flex w-80 flex-col items-center">
        <LogoIcon size={60} className="mb-6 text-blue-500 dark:text-blue-400" />
        <div className="dark:text-polar-400 mb-2 text-center text-gray-500">
          We sent a verification code to{' '}
          <span className="font-bold">{email}</span>
        </div>
        <div className="dark:text-polar-400 mb-6 text-center text-sm text-gray-500">
          Please enter the 6-character code below
        </div>
        <ClientPage return_to={return_to} error={error} email={email} />
      </div>
    </div>
  )
}
