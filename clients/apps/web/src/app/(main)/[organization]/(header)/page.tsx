import { getServerSideAPI } from '@/utils/client/serverside'
import { getStorefrontOrNotFound } from '@/utils/storefront'
import type { Metadata } from 'next'
import ClientPage from './ClientPage'

export async function generateMetadata(props: {
  params: Promise<{ organization: string }>
}): Promise<Metadata> {
  const params = await props.params
  const api = await getServerSideAPI()
  const { organization } = await getStorefrontOrNotFound(
    api,
    params.organization,
  )

  return {
    title: `${organization.name}`, // " | Fluu is added by the template"
    description: `${organization.name} on Fluu`,
    openGraph: {
      title: `${organization.name} on Fluu`,
      description: `${organization.name} on Fluu`,
      siteName: 'Fluu',
      type: 'website',
      images: [
        {
          url: `https://fluu.digital/og?org=${organization.slug}`,
          width: 1200,
          height: 630,
        },
      ],
    },
    twitter: {
      images: [
        {
          url: `https://fluu.digital/og?org=${organization.slug}`,
          width: 1200,
          height: 630,
          alt: `${organization.name} on Fluu`,
        },
      ],
      card: 'summary_large_image',
      title: `${organization.name} on Fluu`,
      description: `${organization.name} on Fluu`,
    },
  }
}

export default async function Page(props: {
  params: Promise<{ organization: string }>
}) {
  const params = await props.params
  const api = await getServerSideAPI()
  const { organization, products } = await getStorefrontOrNotFound(
    api,
    params.organization,
  )

  return <ClientPage organization={organization} products={products} />
}
