import { getServerSideAPI } from '@/utils/client/serverside'
import { getStorefront } from '@/utils/storefront'
import type { Metadata } from 'next'
import { PublicCommunityPage } from '@/components/Community/PublicCommunityPage'
import { mockPublicCommunities } from '@/components/Community/mockPublicCommunities'
import { schemas } from '@polar-sh/client'

// Helper function to get organization (real or mock)
async function getOrganizationOrMock(slug: string) {
  const api = await getServerSideAPI()
  const storefront = await getStorefront(api, slug)

  if (storefront) {
    return storefront.organization
  }

  // Try to find in mock data
  const mockCommunity = mockPublicCommunities.find((c) => c.slug === slug)
  if (mockCommunity) {
    // Create a minimal mock organization object
    // Using Partial to avoid having to define all fields
    const mockOrganization = {
      id: `mock-${slug}`,
      slug: mockCommunity.slug,
      name: mockCommunity.name,
      avatar_url: mockCommunity.avatar_url || null,
      bio: mockCommunity.description,
      email: null,
      website: null,
      socials: [],
      status: 'active' as const,
      details_submitted_at: null,
      feature_settings: null,
      subscription_settings: {
        allow_multiple_subscriptions: false,
        allow_customer_updates: false,
        proration_behavior: 'invoice' as const,
        benefit_revocation_grace_period: 0,
      },
      notification_settings: {
        new_order: false,
        new_subscription: false,
      },
      customer_email_settings: {
        order_confirmation: false,
        subscription_cancellation: false,
        subscription_confirmation: false,
        subscription_cycled: false,
        subscription_past_due: false,
        subscription_revoked: false,
        subscription_uncanceled: false,
        subscription_updated: false,
      },
      created_at: new Date().toISOString(),
      modified_at: new Date().toISOString(),
    } as schemas['Organization']
    return mockOrganization
  }

  return null
}

export async function generateMetadata(props: {
  params: Promise<{ organization: string }>
}): Promise<Metadata> {
  const params = await props.params
  const organization = await getOrganizationOrMock(params.organization)

  if (!organization) {
    return {
      title: 'Community Not Found',
      description: 'The community you are looking for does not exist',
    }
  }

  return {
    title: `${organization.name} Community`,
    description: `Join the ${organization.name} community - Connect, learn, and grow together`,
    openGraph: {
      title: `${organization.name} Community`,
      description: `Join the ${organization.name} community - Connect, learn, and grow together`,
      siteName: 'Fluu',
      type: 'website',
      images: [
        {
          url: `https://fluu.digital/og?org=${organization.slug}`,
          width: 1200,
          height: 630,
        },
      ],
    },
    twitter: {
      images: [
        {
          url: `https://fluu.digital/og?org=${organization.slug}`,
          width: 1200,
          height: 630,
          alt: `${organization.name} Community`,
        },
      ],
      card: 'summary_large_image',
      title: `${organization.name} Community`,
      description: `Join the ${organization.name} community - Connect, learn, and grow together`,
    },
  }
}

export default async function Page(props: {
  params: Promise<{ organization: string }>
}) {
  const params = await props.params
  const organization = await getOrganizationOrMock(params.organization)

  if (!organization) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h1 className="text-2xl font-bold">Community Not Found</h1>
        <p className="text-gray-600 dark:text-polar-400">
          The community you are looking for does not exist
        </p>
      </div>
    )
  }

  return <PublicCommunityPage organization={organization} />
}

