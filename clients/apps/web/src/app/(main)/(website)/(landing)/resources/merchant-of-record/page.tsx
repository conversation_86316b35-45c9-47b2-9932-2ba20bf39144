import { MORPage } from '@/components/Landing/resources/MORPage'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Merchant of Record - Fluu',
  description: 'Entenda como o Merchant of Record simplifica seus pagamentos no Brasil. Receba com PIX sem se preocupar com impostos.',
  keywords:
    'merchant of record, mor, impostos, brasil, pix, pagamentos, compliance, fiscal, fluu',
  openGraph: {
    siteName: 'Fluu',
    type: 'website',
    images: [
      {
        url: 'https://fluu.app.br/assets/brand/fluu_og.jpg',
        width: 1200,
        height: 630,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    images: [
      {
        url: 'https://fluu.app.br/assets/brand/fluu_og.jpg',
        width: 1200,
        height: 630,
        alt: 'Fluu',
      },
    ],
  },
}

export default function Page() {
  return <MORPage />
}
