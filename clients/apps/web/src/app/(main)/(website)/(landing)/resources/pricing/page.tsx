import { PricingPage } from '@/components/Landing/resources/PricingPage'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Preços - Fluu',
  description: 'O MoR mais barato do mercado. Venda com PIX, cartão e boleto sem complicação fiscal.',
  keywords:
    'preços, preço, monetização, pix, cartão, boleto, assinaturas, fluu, preços fluu',
  openGraph: {
    siteName: 'Fluu',
    type: 'website',
    images: [
      {
        url: 'https://fluu.app.br/assets/brand/fluu_og.jpg',
        width: 1200,
        height: 630,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    images: [
      {
        url: 'https://fluu.app.br/assets/brand/fluu_og.jpg',
        width: 1200,
        height: 630,
        alt: 'Fluu',
      },
    ],
  },
}

export default function Page() {
  return <PricingPage />
}
