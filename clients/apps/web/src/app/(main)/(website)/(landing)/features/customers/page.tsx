import { CustomersPage } from '@/components/Landing/features/CustomersPage'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Clientes que Recebem Acesso Automaticamente — Fluu',
  description:
    'Após pagar com PIX, cartão ou boleto, seus clientes recebem acesso automático sem você precisar fazer nada. Área do Cliente sempre atualizada.',
  keywords:
    'gestão de clientes, portal do cliente, acesso automático, pix, cartão, boleto, área do cliente, fluu',
  openGraph: {
    siteName: 'Fluu',
    type: 'website',
    images: [
      {
        url: 'https://fluu.app.br/assets/brand/fluu_og.jpg',
        width: 1200,
        height: 630,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    images: [
      {
        url: 'https://fluu.app.br/assets/brand/fluu_og.jpg',
        width: 1200,
        height: 630,
        alt: 'Fluu',
      },
    ],
  },
}

export default function Page() {
  return <CustomersPage />
}
