import { ProductsPage } from '@/components/Landing/features/ProductsPage'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Produtos Digitais com Entrega Automática — Fluu',
  description:
    'Cobrança flexível com múltiplos modelos de preços, períodos de teste e gestão perfeita de planos. Entrega automática via PIX, cartão ou boleto.',
  keywords:
    'produtos digitais, assinaturas, pagamentos recorrentes, pix, cartão, boleto, entrega automática, fluu',
  openGraph: {
    siteName: 'Fluu',
    type: 'website',
    images: [
      {
        url: 'https://fluu.app.br/assets/brand/fluu_og.jpg',
        width: 1200,
        height: 630,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    images: [
      {
        url: 'https://fluu.app.br/assets/brand/fluu_og.jpg',
        width: 1200,
        height: 630,
        alt: 'Fluu',
      },
    ],
  },
}

export default function Page() {
  return <ProductsPage />
}
