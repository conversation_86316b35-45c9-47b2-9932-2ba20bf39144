'use client'

import FluuCheckout from '@/components/Checkout/FluuCheckout'
import CheckoutLayout from '@/components/Checkout/CheckoutLayout'
import { useCheckout } from '@polar-sh/checkout/providers'

const ClientPage = ({
  embed,
  theme,
}: {
  embed: boolean
  theme?: 'light' | 'dark'
}) => {
  const { checkout } = useCheckout()

  return (
    <CheckoutLayout checkout={checkout} embed={embed} theme={theme}>
      <FluuCheckout embed={embed} theme={theme} />
    </CheckoutLayout>
  )
}

export default ClientPage
