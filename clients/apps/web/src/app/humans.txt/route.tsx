export async function GET() {
  return new Response(
    `
                             ::-=+**########**++=:.                             
                        :=+##########################+=:                        
                    .=*########*#########################*=.                    
                 .=########+-=*#######***###########*-*######=:                 
              :=*######+:.:=######*-.     .-*########*..=#######=:              
            :*######+:  -###*=###=           :*#######*   +########-            
          :*######+.  .+###-.###:             .*#######+   +#####+###:          
         +######+.   =###*. *##:                +#######:   +#####:+##+.        
       .*#####*.   .*###-  *##-                  #######*    +####* -###-       
      =######=    :####-  +##=                   :#######-    *####* :###+      
    .######*     +###*   =###                     =#######     #####=  *##*     
    *#####+     +####.  :###+                      *######:    =####*  .####.   
   =#####+     +####.   +###:                      -######-    :#####.  :###+   
  :#####*     =####-    ####                       .######=     #####-   =###=  
 .######.    :####*    =###=                        +#####+     *####=   .####: 
 +#####:     *####.    *###.                        .#####*     *####+    *###* 
 #####+     +####+    :####                          #####*     +####+    +####.
-#####:    :#####     *###*                          *####*     +####+    =####:
+#####     =####+     ####*                          +####+     *####=    -####=
#####=     #####-    :####*                          +####=     #####-    -####+
#####:    -#####.    -####*                          =####-    .#####.    =####+
*####.    +#####     =####*                          =####:    -####*     *####=
=####:    #####*     =#####                          +####.    +####=     #####-
.####=    #####*     =#####.                         *###*     #####.    -#####.
 +####    ######     =#####=                         ####.    *####:     *####* 
 :####:   *#####     =######                        .###+    =####=     *#####- 
  =###+   =#####.    -######:                       +###-   .####+     =#####*  
   *###:  .#####-    :######=                       ####   .####*     -######   
   .####.  *####+     ######*                      :###.   *####     -######:   
    :###*  -#####:    +######:                     *##=   =###*.    -#####*.    
      +##*  +#####.    ######*                    =##*  .*###=    .*#####*      
       =###: *####+    +######*                  :###: .*###:    =######+       
        :###=.#####:   :#######=                 *##- =###*.   .#######-        
         .+##*=#####:   *#######+               +##- =###-   .+######=          
           .-*#######+. .########*.           :*#*==##*-  .=*######=.           
              -*#######=  *########+:       :*###*##*- .-*######*:              
                .=*#####*-.*##########+--=+#######+:.-*#######+:                
                   :=*#####+###################**=*########=:                   
                       :+*############################*+-.                      
                           .:-==+**#########***+==-:.                           



              Fluu is made by all of our wonderful contributors.

                    https://github.com/fluu/fluu

                Wanna work with us? https://fluu.app.br/company#open-roles

    `,
    {
      headers: {
        // 'Content-Type': 'image/svg+xml',
        'Cache-Control': 'no-cache',
      },
      status: 200,
    },
  )
}
