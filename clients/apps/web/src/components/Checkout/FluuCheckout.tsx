'use client'

import { useCheckoutConfirmedRedirect } from '@/hooks/checkout'
import { usePostHog } from '@/hooks/posthog'
import { useOrganizationPaymentStatus } from '@/hooks/queries/org'
import ArrowBackOutlined from '@mui/icons-material/ArrowBackOutlined'
import {
  CheckoutForm,
  CheckoutProductSwitcher,
  CheckoutPWYWForm,
} from '@polar-sh/checkout/components'
import { useCheckoutFulfillmentListener } from '@polar-sh/checkout/hooks'
import { useCheckout, useCheckoutForm } from '@polar-sh/checkout/providers'
import type { CheckoutConfirmStripe } from '@polar-sh/sdk/models/components/checkoutconfirmstripe'
import type { CheckoutPublicConfirmed } from '@polar-sh/sdk/models/components/checkoutpublicconfirmed'
import type { CheckoutUpdatePublic } from '@polar-sh/sdk/models/components/checkoutupdatepublic'
import { ProductPriceCustom } from '@polar-sh/sdk/models/components/productpricecustom.js'
import { ExpiredCheckoutError } from '@polar-sh/sdk/models/errors/expiredcheckouterror'
import Alert from '@polar-sh/ui/components/atoms/Alert'
import ShadowBox from '@polar-sh/ui/components/atoms/ShadowBox'
import { getThemePreset } from '@polar-sh/ui/hooks/theming'
import type { Stripe, StripeElements } from '@stripe/stripe-js'
import { useTheme } from 'next-themes'
import Link from 'next/link'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { twMerge } from 'tailwind-merge'
import { CheckoutCard } from './CheckoutCard'
import CheckoutProductInfo from './CheckoutProductInfo'

export interface FluuCheckoutProps {
  embed?: boolean
  theme?: 'light' | 'dark'
}

const FluuCheckout = ({ embed: _embed, theme: _theme }: FluuCheckoutProps) => {
  const { client } = useCheckout()
  const {
    checkout,
    form,
    update: _update,
    confirm: _confirm,
    loading: confirmLoading,
    loadingLabel,
    isUpdatePending,
  } = useCheckoutForm()
  const embed = _embed === true
  const { resolvedTheme } = useTheme()
  const theme = _theme || (resolvedTheme as 'light' | 'dark')
  const posthog = usePostHog()

  const themePreset = getThemePreset(checkout.organization.slug, theme)

  // Check organization payment readiness (account verification only for checkout)
  const { data: paymentStatus } = useOrganizationPaymentStatus(
    checkout.organization.id,
    true, // enabled
    true, // accountVerificationOnly - avoid unnecessary product/token checks in checkout
  )

  const isPaymentReady = paymentStatus?.payment_ready ?? true // Default to true while loading
  const isPaymentRequired = checkout.isPaymentRequired
  const shouldBlockCheckout = !isPaymentReady && isPaymentRequired

  // Track checkout page open
  useEffect(() => {
    posthog.capture('storefront:subscriptions:checkout:open', {
      organization_slug: checkout.organization.slug,
      product_id: checkout.product.id,
      amount: checkout.amount,
      embed,
    })
  }, [
    checkout.organization.slug,
    checkout.product.id,
    checkout.amount,
    embed,
    posthog,
  ])

  // Track payment not ready state
  useEffect(() => {
    if (shouldBlockCheckout && paymentStatus) {
      posthog.capture('storefront:subscriptions:payment_not_ready:view', {
        organization_slug: checkout.organization.slug,
        organization_status: paymentStatus?.organization_status,
        product_id: checkout.product.id,
      })
    }
  }, [
    shouldBlockCheckout,
    checkout.organization.slug,
    paymentStatus?.organization_status,
    checkout.product.id,
    posthog,
  ])

  const PaymentNotReadyBanner = () => {
    if (!shouldBlockCheckout) return null

    const isDenied = paymentStatus?.organization_status === 'denied'

    return (
      <Alert color="red">
        <div className="flex flex-col gap-y-2 p-2">
          <div className="font-medium">Pagamentos indisponíveis</div>
          <div className="text-sm">
            {isDenied
              ? `${checkout.organization.name} não permite pagamentos.`
              : `${checkout.organization.name} precisa completar a configuração de pagamentos antes que você possa fazer uma compra.`}
          </div>
        </div>
      </Alert>
    )
  }

  const PixInfoBanner = () => {
    if (!checkout.currency || checkout.currency !== 'brl') return null

    return (
      <div className="rounded-lg bg-green-50 p-4 dark:bg-green-900/20">
        <div className="flex items-center gap-2">
          <div className="text-sm font-medium text-green-800 dark:text-green-200">
            💳 PIX Instantâneo
          </div>
        </div>
        <div className="mt-1 text-xs text-green-700 dark:text-green-300">
          Pagamento instantâneo via PIX. Entrega automática em segundos!
        </div>
      </div>
    )
  }

  const [fullLoading, setFullLoading] = useState(false)
  const loading = useMemo(
    () => confirmLoading || fullLoading,
    [confirmLoading, fullLoading],
  )
  const [listenFulfillment, fullfillmentLabel] = useCheckoutFulfillmentListener(
    client,
    checkout,
  )
  const label = useMemo(
    () => fullfillmentLabel || loadingLabel,
    [fullfillmentLabel, loadingLabel],
  )
  const checkoutConfirmedRedirect = useCheckoutConfirmedRedirect(
    embed,
    theme,
    listenFulfillment,
  )

  const update = useCallback(
    async (data: CheckoutUpdatePublic) => {
      try {
        return await _update(data)
      } catch (error) {
        if (error instanceof ExpiredCheckoutError) {
          window.location.reload()
        }
        throw error
      }
    },
    [_update],
  )

  const confirm = useCallback(
    async (
      data: CheckoutConfirmStripe,
      stripe: Stripe | null,
      elements: StripeElements | null,
    ) => {
      setFullLoading(true)
      let confirmedCheckout: CheckoutPublicConfirmed
      try {
        confirmedCheckout = await _confirm(data, stripe, elements)
      } catch (error) {
        if (error instanceof ExpiredCheckoutError) {
          window.location.reload()
        }
        setFullLoading(false)
        throw error
      }

      await checkoutConfirmedRedirect(
        checkout,
        confirmedCheckout.customerSessionToken,
      )

      return confirmedCheckout
    },
    [_confirm, checkout, checkoutConfirmedRedirect],
  )

  if (embed) {
    return (
      <ShadowBox
        className={twMerge(
          themePreset.polar.checkoutInnerWrapper,
          'flex flex-col gap-y-8 overflow-hidden',
        )}
      >
        <PaymentNotReadyBanner />
        <PixInfoBanner />
        <CheckoutProductSwitcher
          checkout={checkout}
          update={update}
          themePreset={themePreset}
        />
        {checkout.productPrice.amountType === 'custom' && (
          <CheckoutPWYWForm
            checkout={checkout}
            update={update}
            productPrice={checkout.productPrice as ProductPriceCustom}
            themePreset={themePreset}
          />
        )}
        <CheckoutForm
          form={form}
          checkout={checkout}
          update={update}
          confirm={confirm}
          loading={loading}
          loadingLabel={label}
          theme={theme}
          themePreset={themePreset}
          disabled={shouldBlockCheckout}
          isUpdatePending={isUpdatePending}
        />
      </ShadowBox>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-6">
          {checkout.returnUrl && (
            <Link
              href={checkout.returnUrl}
              className="dark:text-polar-500 inline-flex items-center gap-x-2 text-gray-500 hover:text-gray-700"
            >
              <ArrowBackOutlined fontSize="inherit" />
              <span>Voltar para {checkout.organization.name}</span>
            </Link>
          )}
        </div>

        <div className="grid gap-6 lg:grid-cols-2 lg:gap-8">
          {/* Product Info - Mobile First */}
          <div className="order-2 lg:order-1">
            <ShadowBox className="p-6">
              <CheckoutProductInfo
                organization={checkout.organization}
                product={checkout.product}
              />
              <CheckoutProductSwitcher
                checkout={checkout}
                update={update}
                themePreset={themePreset}
              />
              {checkout.productPrice.amountType === 'custom' && (
                <CheckoutPWYWForm
                  checkout={checkout}
                  update={update}
                  productPrice={checkout.productPrice as ProductPriceCustom}
                  themePreset={themePreset}
                />
              )}
              <CheckoutCard
                checkout={checkout}
                update={update}
                themePreset={themePreset}
              />
            </ShadowBox>
          </div>

          {/* Checkout Form - Mobile First */}
          <div className="order-1 lg:order-2">
            <ShadowBox className="p-6">
              <PaymentNotReadyBanner />
              <PixInfoBanner />
              <CheckoutForm
                form={form}
                checkout={checkout}
                update={update}
                confirm={confirm}
                loading={loading}
                loadingLabel={label}
                theme={theme}
                themePreset={themePreset}
                disabled={shouldBlockCheckout}
                isUpdatePending={isUpdatePending}
              />
            </ShadowBox>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FluuCheckout