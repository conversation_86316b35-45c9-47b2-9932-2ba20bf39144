import { schemas } from '@polar-sh/client'
import { Check, Download, Gauge, Key } from 'lucide-react'
import { twMerge } from 'tailwind-merge'
import GitHubIcon from '../Icons/GitHubIcon'

export type CreatableBenefit = schemas['BenefitType']

export const resolveBenefitCategoryIcon = (
  type?: schemas['BenefitType'] | 'telegram' | 'whatsapp',
  className?: string,
) => {
  const cn = twMerge('h-4 w-4', className)
  switch (type) {
    case 'discord':
      return <DiscordIcon className={cn} />
    case 'telegram':
      return <TelegramIcon className={cn} />
    case 'whatsapp':
      return <WhatsAppIcon className={cn} />
    case 'github_repository':
      return <GitHubIcon className={cn} />
    case 'downloadables':
      return <Download className={cn} />
    case 'license_keys':
      return <Key className={cn} />
    case 'meter_credit':
      return <Gauge className={cn} />
    default:
      return <Check className={cn} />
  }
}

export const resolveBenefitIcon = (
  type: schemas['BenefitType'] | 'telegram' | 'whatsapp',
  className?: string,
) => {
  return resolveBenefitCategoryIcon(type, className)
}

export const DiscordIcon = ({
  className,
  size = 16,
}: {
  className?: string
  size?: number
}) => (
  <svg
    className={className}
    viewBox="0 -28.5 256 256"
    version="1.1"
    preserveAspectRatio="xMidYMid"
    fill="currentColor"
    width={size}
    height={size}
  >
    <g>
      <path
        d="M216.856339,16.5966031 C200.285002,8.84328665 182.566144,3.2084988 164.041564,0 C161.766523,4.11318106 159.108624,9.64549908 157.276099,14.0464379 C137.583995,11.0849896 118.072967,11.0849896 98.7430163,14.0464379 C96.9108417,9.64549908 94.1925838,4.11318106 91.8971895,0 C73.3526068,3.2084988 55.6133949,8.86399117 39.0420583,16.6376612 C5.61752293,67.146514 -3.4433191,116.400813 1.08711069,164.955721 C23.2560196,181.510915 44.7403634,191.567697 65.8621325,198.148576 C71.0772151,190.971126 75.7283628,183.341335 79.7352139,175.300261 C72.104019,172.400575 64.7949724,168.822202 57.8887866,164.667963 C59.7209612,163.310589 61.5131304,161.891452 63.2445898,160.431257 C105.36741,180.133187 151.134928,180.133187 192.754523,160.431257 C194.506336,161.891452 196.298154,163.310589 198.110326,164.667963 C191.183787,168.842556 183.854737,172.420929 176.223542,175.320965 C180.230393,183.341335 184.861538,190.991831 190.096624,198.16893 C211.238746,191.588051 232.743023,181.531619 254.911949,164.955721 C260.227747,108.668201 245.831087,59.8662432 216.856339,16.5966031 Z M85.4738752,135.09489 C72.8290281,135.09489 62.4592217,123.290155 62.4592217,108.914901 C62.4592217,94.5396472 72.607595,82.7145587 85.4738752,82.7145587 C98.3405064,82.7145587 108.709962,94.5189427 108.488529,108.914901 C108.508531,123.290155 98.3405064,135.09489 85.4738752,135.09489 Z M170.525237,135.09489 C157.88039,135.09489 147.510584,123.290155 147.510584,108.914901 C147.510584,94.5396472 157.658606,82.7145587 170.525237,82.7145587 C183.391518,82.7145587 193.761324,94.5189427 193.539891,108.914901 C193.539891,123.290155 183.391518,135.09489 170.525237,135.09489 Z"
        fillRule="nonzero"
      ></path>
    </g>
  </svg>
)

export const TelegramIcon = ({
  className,
  size = 16,
}: {
  className?: string
  size?: number
}) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="currentColor"
    width={size}
    height={size}
  >
    <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
  </svg>
)

export const WhatsAppIcon = ({
  className,
  size = 16,
}: {
  className?: string
  size?: number
}) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="currentColor"
    width={size}
    height={size}
  >
    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.74 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.965 3.505"/>
  </svg>
)

export const benefitsDisplayNames: {
  [key in schemas['BenefitType'] | 'telegram' | 'whatsapp' | 'usage']: string
} = {
  usage: 'Usage',
  license_keys: 'License Keys',
  github_repository: 'GitHub Repository Access',
  discord: 'Discord Invite',
  telegram: 'Telegram Group Access',
  whatsapp: 'WhatsApp Group Access',
  downloadables: 'File Downloads',
  custom: 'Custom',
  meter_credit: 'Meter Credits',
}
