import { schemas } from '@polar-sh/client'
import Pill from '@polar-sh/ui/components/atoms/Pill'
import { Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface PixStatusProps {
  order: schemas['CustomerOrder'] & {
    payment_processor_metadata?: {
      pix_status?: string
      status?: string
      processing_time_seconds?: number
      transaction_id?: string
    }
  }
}

export const PixStatus = ({ order }: PixStatusProps) => {
  const getPixStatusInfo = () => {
    if (!order.payment_processor_metadata) {
      return { status: 'unknown', label: 'Status desconhecido', icon: AlertCircle, color: 'gray' as const, description: 'Status do PIX não identificado' }
    }

    const metadata = order.payment_processor_metadata
    const pixStatus = metadata.pix_status || metadata.status
    
    switch (pixStatus) {
      case 'pending':
        return {
          status: 'pending',
          label: 'PIX Pendente',
          icon: Clock,
          color: 'yellow',
          description: 'Aguardando pagamento do PIX'
        }
      case 'paid':
        return {
          status: 'paid',
          label: 'PIX Pago',
          icon: CheckCircle,
          color: 'green',
          description: 'PIX confirmado com sucesso'
        }
      case 'expired':
        return {
          status: 'expired',
          label: 'PIX Expirado',
          icon: XCircle,
          color: 'red',
          description: 'Código PIX expirado'
        }
      case 'failed':
        return {
          status: 'failed',
          label: 'PIX Falhou',
          icon: XCircle,
          color: 'red',
          description: 'Pagamento PIX falhou'
        }
      default:
        return {
          status: 'unknown',
          label: 'Status desconhecido',
          icon: AlertCircle,
          color: 'gray',
          description: 'Status do PIX não identificado'
        }
    }
  }

  const getProcessingTime = () => {
    if (!order.payment_processor_metadata) return null
    
    const metadata = order.payment_processor_metadata
    const processingTime = metadata.processing_time_seconds
    
    if (processingTime) {
      return processingTime < 3 
        ? `${processingTime}s - Entrega instantânea ⚡`
        : `${processingTime}s - Entrega rápida`
    }
    
    return null
  }

  const pixInfo = getPixStatusInfo()
  const processingTime = getProcessingTime()
  const Icon = pixInfo.icon

  return (
    <div className="space-y-2">
      <Pill
        color={pixInfo.color as 'gray' | 'blue' | 'purple' | 'yellow' | 'red' | 'green'}
        className="inline-flex items-center gap-1"
      >
        <Icon className="h-3 w-3" />
        {pixInfo.label}
      </Pill>
      
      <div className="text-sm text-gray-600 dark:text-gray-400">
        {pixInfo.description}
      </div>
      
      {processingTime && (
        <div className="text-xs text-green-600 dark:text-green-400 font-medium">
          {processingTime}
        </div>
      )}
      
      {order.payment_processor_metadata && (
        <div className="text-xs text-gray-500 dark:text-gray-500">
          ID da transação: {order.payment_processor_metadata.transaction_id || 'N/A'}
        </div>
      )}
    </div>
  )
}