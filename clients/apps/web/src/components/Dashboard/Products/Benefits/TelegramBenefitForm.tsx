import { useFormContext } from 'react-hook-form'
import { schemas } from '@polar-sh/client'
import Input from '@polar-sh/ui/components/atoms/Input'
import TextArea from '@polar-sh/ui/components/atoms/TextArea'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@polar-sh/ui/components/ui/form'

export const TelegramBenefitForm = () => {
  const { control } = useFormContext<any>()

  return (
    <>
      <FormField
        control={control}
        name="properties.bot_token"
        rules={{
          required: 'Bot token is required',
          pattern: {
            value: /^\d+:[A-Za-z0-9_-]+$/,
            message: 'Invalid bot token format',
          },
        }}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Bot Token</FormLabel>
            <FormControl>
              <Input
                {...field}
                value={field.value || ''}
                placeholder="123456789:ABCdefGHIjklMNOpqrsTUVwxyz"
              />
            </FormControl>
            <FormDescription>
              Your Telegram bot token from @BotFather
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="properties.chat_id"
        rules={{
          required: 'Chat ID is required',
        }}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Group/Channel ID</FormLabel>
            <FormControl>
              <Input
                {...field}
                value={field.value || ''}
                placeholder="-1001234567890"
              />
            </FormControl>
            <FormDescription>
              The ID of your Telegram group or channel (include -100 prefix for groups)
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="properties.group_link"
        rules={{
          pattern: {
            value: /^https?:\/\/(t\.me|telegram\.me)\/.+/,
            message: 'Invalid Telegram link format',
          },
        }}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Group Link (Optional)</FormLabel>
            <FormControl>
              <Input
                {...field}
                value={field.value || ''}
                placeholder="https://t.me/yourgroup"
              />
            </FormControl>
            <FormDescription>
              Public link to your Telegram group/channel for customer access
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="properties.welcome_message"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Welcome Message (Optional)</FormLabel>
            <FormControl>
              <TextArea
                {...field}
                value={field.value || ''}
                placeholder="Welcome to our exclusive Telegram group! 🎉"
                rows={3}
              />
            </FormControl>
            <FormDescription>
              Message sent to users when they join your Telegram group
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  )
}