import { useFormContext } from 'react-hook-form'
import { schemas } from '@polar-sh/client'
import Input from '@polar-sh/ui/components/atoms/Input'
import TextArea from '@polar-sh/ui/components/atoms/TextArea'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@polar-sh/ui/components/ui/form'

export const WhatsAppBenefitForm = () => {
  const { control } = useFormContext<any>()

  return (
    <>
      <FormField
        control={control}
        name="properties.group_name"
        rules={{
          required: 'Group name is required',
          maxLength: {
            value: 100,
            message: 'Group name must be less than 100 characters',
          },
        }}
        render={({ field }) => (
          <FormItem>
            <FormLabel>WhatsApp Group Name</FormLabel>
            <FormControl>
              <Input
                {...field}
                value={field.value || ''}
                placeholder="Exclusive Creators Group"
              />
            </FormControl>
            <FormDescription>
              Name of your WhatsApp group for members
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="properties.invite_link"
        rules={{
          pattern: {
            value: /^https?:\/\/(chat\.whatsapp\.com)\/.+/,
            message: 'Invalid WhatsApp invite link format',
          },
        }}
        render={({ field }) => (
          <FormItem>
            <FormLabel>WhatsApp Group Invite Link</FormLabel>
            <FormControl>
              <Input
                {...field}
                value={field.value || ''}
                placeholder="https://chat.whatsapp.com/AbCdEfGhIjKlMnOp"
              />
            </FormControl>
            <FormDescription>
              WhatsApp group invite link (expires in 3 days - renew regularly)
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="properties.instructions"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Access Instructions (Optional)</FormLabel>
            <FormControl>
              <TextArea
                {...field}
                value={field.value || ''}
                placeholder="After joining, please introduce yourself and read the group rules."
                rows={3}
              />
            </FormControl>
            <FormDescription>
              Instructions for customers on how to access and use the WhatsApp group
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="properties.contact_email"
        rules={{
          pattern: {
            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
            message: 'Invalid email format',
          },
        }}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Contact Email (Optional)</FormLabel>
            <FormControl>
              <Input
                {...field}
                value={field.value || ''}
                placeholder="<EMAIL>"
              />
            </FormControl>
            <FormDescription>
              Email for WhatsApp group management (used for notifications)
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  )
}