'use client'

import { PolarLogotype } from '@/components/Layout/Public/PolarLogotype'
import Footer from '@/components/Organization/Footer'
import { usePostHog } from '@/hooks/posthog'
import Button from '@polar-sh/ui/components/atoms/Button'
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarProvider,
  SidebarTrigger,
  useSidebar,
} from '@polar-sh/ui/components/atoms/Sidebar'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ComponentProps, PropsWithChildren } from 'react'
import { twMerge } from 'tailwind-merge'
import { AuthModal } from '../Auth/AuthModal'
import { Modal } from '../Modal'
import { useModal } from '../Modal/useModal'
import { NavPopover, NavPopoverSection } from './NavPopover'
import { mockPublicCommunities } from '../Community/mockPublicCommunities'

export default function Layout({ children }: PropsWithChildren) {
  return (
    <div className="dark:bg-polar-950 relative flex flex-col bg-gray-50 px-0 md:w-full md:flex-1 md:items-center md:px-4">
      <div className="flex flex-col gap-y-2 md:w-full">
        <LandingPageDesktopNavigation />
        <SidebarProvider className="absolute inset-0 flex flex-col items-start md:hidden">
          <LandingPageTopbar />
          <LandingPageMobileNavigation />
        </SidebarProvider>

        <div className="dark:bg-polar-950 relative flex flex-col px-4 pt-32 md:w-full md:px-0 md:pt-0">
          {children}
        </div>
        <LandingPageFooter />
      </div>
    </div>
  )
}

const NavLink = ({
  href,
  className,
  children,
  isActive: _isActive,
  target,
  ...props
}: ComponentProps<typeof Link> & {
  isActive?: (pathname: string) => boolean
}) => {
  const pathname = usePathname()
  const isActive = _isActive
    ? _isActive(pathname)
    : pathname.startsWith(href.toString())
  const isExternal = href.toString().startsWith('http')

  return (
    <Link
      href={href}
      target={isExternal ? '_blank' : target}
      prefetch
      className={twMerge(
        'dark:text-polar-500 -m-1 flex items-center gap-x-2 p-1 text-gray-500 transition-colors hover:text-black dark:hover:text-white',
        isActive && 'text-black dark:text-white',
        className,
      )}
      {...props}
    >
      {children}
    </Link>
  )
}

interface NavigationItem {
  title: string
  href: string
  isActive?: (pathname: string) => boolean
  target?: '_blank'
}

const mobileNavigationItems: NavigationItem[] = [
  {
    title: 'Início',
    href: '/',
    isActive: (pathname) => pathname === '/',
  },
  {
    title: 'Recursos',
    href: '/features/products',
  },
  {
    title: 'PIX e Pagamentos',
    href: '/features/finance',
  },
  {
    title: 'Comunidades',
    href: '/features/products',
  },
  {
    title: 'Assinaturas',
    href: '/features/usage-billing',
  },
  {
    title: 'Preços',
    href: '/resources/pricing',
  },
  {
    title: 'Sobre',
    href: '/company',
  },
]

const LandingPageMobileNavigation = () => {
  const sidebar = useSidebar()

  const posthog = usePostHog()
  const { isShown: isModalShown, hide: hideModal, show: showModal } = useModal()

  const onLoginClick = () => {
    posthog.capture('global:user:login:click')
    sidebar.toggleSidebar()
    showModal()
  }

  return (
    <>
      <Sidebar className="md:hidden">
        <SidebarHeader className="p-4">
          <PolarLogotype logoVariant="icon" />
        </SidebarHeader>
        <SidebarContent className="flex flex-col gap-y-6 px-6 py-2">
          <div className="flex flex-col gap-y-1">
            {mobileNavigationItems.map((item) => {
              return (
                <NavLink
                  key={item.title}
                  className="text-xl tracking-tight"
                  isActive={item.isActive}
                  target={item.target}
                  href={item.href}
                  onClick={sidebar.toggleSidebar}
                >
                  {item.title}
                </NavLink>
              )
            })}
          </div>
          <div className="flex flex-col gap-y-2 border-t border-gray-200 dark:border-polar-700 pt-4">
            <h3 className="text-sm font-semibold text-gray-700 dark:text-polar-300 mb-2">
              Comunidades Populares
            </h3>
            {mockPublicCommunities.slice(0, 4).map((community) => (
              <NavLink
                key={community.slug}
                href={`/${community.slug}/community`}
                className="text-base tracking-tight"
                onClick={sidebar.toggleSidebar}
              >
                <div className="flex flex-col">
                  <span>{community.name}</span>
                  <span className="text-xs text-gray-500 dark:text-polar-500">
                    {community.memberCount.toLocaleString()} membros
                  </span>
                </div>
              </NavLink>
            ))}
          </div>
          <NavLink
            href="#"
            onClick={onLoginClick}
            className="text-xl tracking-tight"
          >
            Entrar
          </NavLink>
        </SidebarContent>
      </Sidebar>
      <Modal
        title="Entrar"
        isShown={isModalShown}
        hide={hideModal}
        modalContent={<AuthModal />}
        className="lg:w-full lg:max-w-[480px]"
      />
    </>
  )
}

const LandingPageDesktopNavigation = () => {
  const posthog = usePostHog()
  const { isShown: isModalShown, hide: hideModal, show: showModal } = useModal()
  const pathname = usePathname()

  const onLoginClick = () => {
    posthog.capture('global:user:login:click')
    showModal()
  }

  const featuresSections: NavPopoverSection[] = [
    {
      title: 'Para Creators',
      items: [
        {
          href: '/features/products',
          label: 'Produtos Digitais',
          subtitle: 'Cursos, ebooks, acessos VIP',
        },
        {
          href: '/features/finance',
          label: 'PIX e Pagamentos',
          subtitle: 'PIX instantâneo, cartão e boleto',
        },
        {
          href: '/features/usage-billing',
          label: 'Assinaturas',
          subtitle: 'Cobrança recorrente automática',
        },
        {
          href: '/features/benefits',
          label: 'Entregas Automáticas',
          subtitle: 'Acesso automático após pagamento',
        },
      ],
    },
    {
      title: 'Comunidade',
      items: [
        {
          href: '/features/products',
          label: 'Comunidades Exclusivas',
          subtitle: 'Feed, eventos, leaderboard',
        },
        {
          href: '/features/customers',
          label: 'Gestão de Membros',
          subtitle: 'Perfis e engajamento',
        },
        {
          href: '/features/analytics',
          label: 'Analytics',
          subtitle: 'Insights de receita e crescimento',
        },
      ],
    },
  ]

  const communitiesSections: NavPopoverSection[] = [
    {
      title: 'Comunidades Populares',
      items: [
        ...mockPublicCommunities.slice(0, 5).map((community) => ({
          href: `/${community.slug}/community`,
          label: community.name,
          subtitle: `${community.memberCount.toLocaleString()} membros • ${community.category}`,
        })),
        {
          href: '/communities',
          label: 'Ver todas as comunidades',
          subtitle: 'Explore todas as comunidades disponíveis',
        },
      ],
    },
  ]


  return (
    <div className="dark:text-polar-50 hidden w-full flex-col items-center gap-12 py-8 md:flex">
      <div className="relative flex w-full flex-row items-center justify-between lg:max-w-6xl">
        <PolarLogotype logoVariant="icon" size={40} />

        <ul className="absolute left-1/2 mx-auto flex -translate-x-1/2 flex-row gap-x-8 font-medium">
          <li>
            <NavPopover
              trigger="Recursos"
              sections={featuresSections}
              isActive={pathname.startsWith('/features')}
            />
          </li>
          <li>
            <NavPopover
              trigger="Comunidades"
              sections={communitiesSections}
              isActive={pathname.includes('/community')}
            />
          </li>
          <li>
            <NavLink href="/resources/pricing">Preços</NavLink>
          </li>
          <li>
            <NavLink href="/company">Sobre</NavLink>
          </li>
        </ul>

        <Button onClick={onLoginClick} variant="ghost" className="rounded-full">
          Entrar
        </Button>
      </div>
      <Modal
        title="Entrar"
        isShown={isModalShown}
        hide={hideModal}
        modalContent={<AuthModal />}
        className="lg:w-full lg:max-w-[480px]"
      />
    </div>
  )
}

const LandingPageTopbar = () => {
  return (
    <div className="z-30 flex w-full flex-row items-center justify-between px-6 py-6 md:hidden md:px-12">
      <PolarLogotype
        className="mt-1 ml-2 md:hidden"
        logoVariant="logotype"
        size={100}
      />
      <SidebarTrigger className="md:hidden" />
    </div>
  )
}

const LandingPageFooter = () => {
  return (
    <motion.div
      initial="initial"
      className="relative flex w-full flex-col items-center"
      variants={{ initial: { opacity: 0 }, animate: { opacity: 1 } }}
      transition={{ duration: 0.5, ease: 'easeInOut' }}
      whileInView="animate"
      viewport={{ once: true }}
    >
      <Footer />
    </motion.div>
  )
}
