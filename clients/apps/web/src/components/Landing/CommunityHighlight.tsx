'use client'

import GroupsOutlined from '@mui/icons-material/GroupsOutlined'
import MessageOutlined from '@mui/icons-material/MessageOutlined'
import BookOutlined from '@mui/icons-material/BookOutlined'
import CalendarTodayOutlined from '@mui/icons-material/CalendarTodayOutlined'
import EmojiEventsOutlined from '@mui/icons-material/EmojiEventsOutlined'
import ArrowOutwardOutlined from '@mui/icons-material/ArrowOutwardOutlined'
import Button from '@polar-sh/ui/components/atoms/Button'
import { Card, CardContent } from '@polar-sh/ui/components/atoms/Card'
import Avatar from '@polar-sh/ui/components/atoms/Avatar'
import { Heart, MessageCircle, Pin } from 'lucide-react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { twMerge } from 'tailwind-merge'

const communityFeatures = [
  {
    icon: <MessageOutlined fontSize="inherit" />,
    text: 'Feed de Discussões',
  },
  {
    icon: <BookOutlined fontSize="inherit" />,
    text: 'Área de Aprendizado',
  },
  {
    icon: <CalendarTodayOutlined fontSize="inherit" />,
    text: 'Eventos e Webinars',
  },
  {
    icon: <EmojiEventsOutlined fontSize="inherit" />,
    text: 'Leaderboard',
  },
]

const mockPost = {
  author: {
    name: 'Jonah Cockshaw',
    avatar_url: null,
    badge: '3',
  },
  title: 'Group Coaching Calls!',
  content:
    "Hey @everyone - I have added new group coaching calls to the calendar starting from tomorrow. They're at: Tuesday and Thursday: 3pm UK 10am EST 7am PST...",
  category: 'General Discussion',
  timeAgo: '3 days ago',
  likes: 2,
  comments: 0,
  icon: <Pin className="h-4 w-4" />,
}

export const CommunityHighlight = () => {
  return (
    <div className="dark:bg-polar-900 flex w-full flex-col overflow-hidden rounded-4xl bg-white">
      <div className="flex flex-col items-center gap-y-8 px-8 pt-8 md:px-16 md:pt-16">
        <div className="flex flex-row items-center gap-2">
          <GroupsOutlined className="h-6 w-6 text-blue-500" />
          <span className="dark:text-polar-500 text-lg text-gray-400">
            Comunidades Exclusivas
          </span>
        </div>
        <h1 className="w-fit max-w-3xl text-center text-2xl text-pretty md:text-4xl md:leading-normal dark:text-white">
          Crie e gerencie comunidades engajadas com ferramentas profissionais
        </h1>
        <p className="dark:text-polar-400 max-w-2xl text-center text-lg text-gray-500">
          Feed de discussões, área de aprendizado, eventos, leaderboard e muito
          mais. Tudo integrado em uma plataforma completa.
        </p>
        <Link href="/features/products">
          <Button
            fullWidth
            wrapperClassNames="flex flex-row items-center gap-x-2"
            variant="secondary"
            className="rounded-full max-w-xs"
          >
            <span>Explorar Comunidades</span>
            <ArrowOutwardOutlined fontSize="inherit" />
          </Button>
        </Link>
      </div>

      <div className="flex flex-col gap-6 px-8 pb-8 md:px-16 md:pb-16 mt-6">
        {/* Features Grid */}
        <div className="grid grid-cols-2 gap-3 md:grid-cols-4">
          {communityFeatures.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className="dark:bg-polar-800 dark:border-polar-700 flex flex-col items-center gap-2 rounded-xl border border-gray-200 bg-gray-50 p-4"
            >
              <div className="dark:bg-polar-700 flex h-10 w-10 items-center justify-center rounded-lg bg-white text-blue-500 text-xl">
                {feature.icon}
              </div>
              <span className="dark:text-polar-200 text-center text-xs font-medium text-gray-700 md:text-sm">
                {feature.text}
              </span>
            </motion.div>
          ))}
        </div>

        {/* Mock Community Post Preview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="dark:bg-polar-800 dark:border-polar-700 overflow-hidden rounded-2xl border border-gray-200 bg-white"
        >
          <Card className="border-0 shadow-none">
            <CardContent className="p-6">
              <div className="flex flex-col gap-4">
                {/* Header */}
                <div className="flex flex-row items-start justify-between">
                  <div className="flex flex-row items-center gap-3">
                    <div className="relative">
                      <Avatar
                        name={mockPost.author.name}
                        avatar_url={mockPost.author.avatar_url}
                        className="h-10 w-10"
                      />
                      {mockPost.author.badge && (
                        <div className="absolute -bottom-1 -right-1 h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center border-2 border-white dark:border-polar-800">
                          <span className="text-xs text-white font-semibold">
                            {mockPost.author.badge}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="flex flex-col">
                      <div className="flex flex-row items-center gap-2">
                        <span className="font-semibold dark:text-white">
                          {mockPost.author.name}
                        </span>
                        <span className="text-gray-500 dark:text-polar-400">
                          {mockPost.icon}
                        </span>
                        <span className="font-medium dark:text-white">
                          {mockPost.title}
                        </span>
                      </div>
                      <div className="flex flex-row items-center gap-2 text-sm text-gray-500 dark:text-polar-400">
                        <span>{mockPost.timeAgo}</span>
                        <span>•</span>
                        <span>{mockPost.category}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-1 text-yellow-500">
                    <Pin className="h-4 w-4" />
                  </div>
                </div>

                {/* Content */}
                <p className="dark:text-polar-200 text-gray-700 leading-relaxed">
                  {mockPost.content}
                </p>

                {/* Actions */}
                <div className="flex flex-row items-center gap-6 pt-2 border-t border-gray-200 dark:border-polar-700">
                  <button className="flex flex-row items-center gap-2 text-gray-600 dark:text-polar-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors">
                    <Heart className="h-5 w-5" />
                    <span>{mockPost.likes}</span>
                  </button>
                  <button className="flex flex-row items-center gap-2 text-gray-600 dark:text-polar-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors">
                    <MessageCircle className="h-5 w-5" />
                    <span>{mockPost.comments}</span>
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

