'use client'

import { createClientSideAPI } from '@/utils/client'
import AllInclusiveOutlined from '@mui/icons-material/AllInclusiveOutlined'
import DiamondOutlined from '@mui/icons-material/DiamondOutlined'
import DownloadIcon from '@mui/icons-material/Download'
import GroupIcon from '@mui/icons-material/Group'
import { schemas } from '@polar-sh/client'
import Button from '@polar-sh/ui/components/atoms/Button'
import ShadowBox from '@polar-sh/ui/components/atoms/ShadowBox'
import { CurrentPeriodOverview } from './CurrentPeriodOverview'
import { CustomerPortalGrants } from './CustomerPortalGrants'
import {
  ActiveSubscriptionsOverview,
  InactiveSubscriptionsOverview,
} from './CustomerPortalSubscriptions'
import { EmptyState } from './EmptyState'

export interface FluuPortalProps {
  organization: schemas['Organization']
  products: schemas['CustomerProduct'][]
  subscriptions: schemas['CustomerSubscription'][]
  claimedSubscriptions: schemas['CustomerSubscription'][]
  benefitGrants: schemas['CustomerBenefitGrant'][]
  customerSessionToken: string
}

export const FluuPortalOverview = ({
  organization,
  products,
  subscriptions,
  claimedSubscriptions,
  benefitGrants,
  customerSessionToken,
}: FluuPortalProps) => {
  const api = createClientSideAPI(customerSessionToken)

  const activeOwnedSubscriptions = subscriptions.filter(
    (s) => s.status === 'active' || s.status === 'trialing',
  )
  const inactiveOwnedSubscriptions = subscriptions.filter(
    (s) => s.status !== 'active' && s.status !== 'trialing',
  )

  const activeClaimedSubscriptions = claimedSubscriptions.filter(
    (s) => s.status === 'active' || s.status === 'trialing',
  )

  const hasAnyActiveSubscriptions =
    activeOwnedSubscriptions.length > 0 || activeClaimedSubscriptions.length > 0

  const hasBenefits = benefitGrants.length > 0

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="mx-auto max-w-6xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Meus Produtos
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Acesse seus produtos digitais e benefícios exclusivos
          </p>
        </div>

        {/* Active Products Grid */}
        {hasAnyActiveSubscriptions && (
          <div className="mb-8">
            <div className="mb-6 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Produtos Ativos
              </h2>
              <div className="rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
                {activeOwnedSubscriptions.length + activeClaimedSubscriptions.length} ativo(s)
              </div>
            </div>
            
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {activeOwnedSubscriptions.map((subscription) => (
                <ProductCard
                  key={subscription.id}
                  subscription={subscription}
                  type="owned"
                  api={api}
                />
              ))}
              {activeClaimedSubscriptions.map((subscription) => (
                <ProductCard
                  key={subscription.id}
                  subscription={subscription}
                  type="claimed"
                  api={api}
                />
              ))}
            </div>
          </div>
        )}

        {/* Benefits Section */}
        {hasBenefits && (
          <div className="mb-8">
            <div className="mb-6 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Benefícios Exclusivos
              </h2>
              <div className="rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {benefitGrants.length} benefício(s)
              </div>
            </div>
            
            <ShadowBox className="p-6">
              <CustomerPortalGrants
                organization={organization}
                benefitGrants={benefitGrants}
                api={api}
              />
            </ShadowBox>
          </div>
        )}

        {/* Inactive Products */}
        {inactiveOwnedSubscriptions.length > 0 && (
          <div className="mb-8">
            <h2 className="mb-6 text-xl font-semibold text-gray-900 dark:text-white">
              Produtos Inativos
            </h2>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {inactiveOwnedSubscriptions.map((subscription) => (
                <ProductCard
                  key={subscription.id}
                  subscription={subscription}
                  type="inactive"
                  api={api}
                />
              ))}
            </div>
          </div>
        )}

        {/* Empty State */}
        {!hasAnyActiveSubscriptions && !hasBenefits && (
          <div className="text-center">
            <EmptyState
              icon={
                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800">
                  <DiamondOutlined className="h-6 w-6 text-gray-400" />
                </div>
              }
              title="Nenhum produto encontrado"
              description="Você ainda não comprou nenhum produto. Explore nossos produtos digitais!"
            />
            <Button variant="secondary" className="mt-4">
              Explorar Produtos
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

interface ProductCardProps {
  subscription: schemas['CustomerSubscription']
  type: 'owned' | 'claimed' | 'inactive'
  api: any
}

const ProductCard = ({ subscription, type, api }: ProductCardProps) => {
  const getStatusColor = () => {
    switch (type) {
      case 'owned':
      case 'claimed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'inactive':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
    }
  }

  const getStatusText = () => {
    switch (type) {
      case 'owned':
        return 'Ativo'
      case 'claimed':
        return 'Acesso Concedido'
      case 'inactive':
        return 'Inativo'
    }
  }

  return (
    <ShadowBox className="p-6 hover:shadow-lg transition-shadow">
      <div className="mb-4 flex items-start justify-between">
        <div>
          <h3 className="font-semibold text-gray-900 dark:text-white">
            {subscription.product.name}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {subscription.product.description}
          </p>
        </div>
        <span className={`rounded-full px-2 py-1 text-xs font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </div>

      <div className="mb-4 space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Próxima cobrança:</span>
          <span className="font-medium text-gray-900 dark:text-white">
            {subscription.current_period_end ? new Date(subscription.current_period_end).toLocaleDateString('pt-BR') : '—'}
          </span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Valor:</span>
          <span className="font-medium text-gray-900 dark:text-white">
            {subscription.amount && subscription.currency
              ? new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: subscription.currency,
                }).format(subscription.amount / 100)
              : '—'}
          </span>
        </div>
      </div>

      <div className="flex gap-2">
        <Button 
          variant="secondary" 
          size="sm" 
          className="flex-1"
          onClick={() => {/* Handle view details */}}
        >
          <DownloadIcon className="mr-1 h-4 w-4" />
          Acessar Produto
        </Button>
        {type === 'owned' && (
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => {/* Handle manage */}}
          >
            <GroupIcon className="mr-1 h-4 w-4" />
            Gerenciar
          </Button>
        )}
      </div>
    </ShadowBox>
  )
}