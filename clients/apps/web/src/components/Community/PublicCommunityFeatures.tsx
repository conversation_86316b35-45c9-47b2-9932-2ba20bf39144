'use client'

import { MessageSquare, BookOpen, Calendar, Trophy, Users, Sparkles } from 'lucide-react'
import Image from 'next/image'

const features = [
  {
    icon: MessageSquare,
    title: 'Discussion Forums',
    description:
      'Engage in meaningful conversations with community members on topics that matter to you.',
    image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=600&q=80',
  },
  {
    icon: BookOpen,
    title: 'Learning Resources',
    description:
      'Access exclusive courses, tutorials, and educational content curated by experts.',
    image: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=600&q=80',
  },
  {
    icon: Calendar,
    title: 'Live Events',
    description:
      'Join weekly webinars, workshops, and networking events with industry leaders.',
    image: 'https://images.unsplash.com/photo-1511578314322-379afb476865?w=600&q=80',
  },
  {
    icon: Trophy,
    title: 'Leaderboard',
    description:
      'Compete and climb the ranks by actively participating and helping others.',
    image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=600&q=80',
  },
  {
    icon: Users,
    title: 'Member Directory',
    description:
      'Connect with other members, find collaborators, and build your network.',
    image: 'https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=600&q=80',
  },
  {
    icon: Sparkles,
    title: 'Exclusive Content',
    description:
      'Get early access to new features, beta programs, and premium resources.',
    image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=600&q=80',
  },
]

export const PublicCommunityFeatures = () => {
  return (
    <div className="w-full bg-white dark:bg-polar-900 py-16 md:py-24">
      <div className="max-w-6xl mx-auto px-4 md:px-8">
        <div className="flex flex-col items-center gap-4 mb-12">
          <h2 className="text-3xl md:text-4xl font-bold dark:text-white text-center">
            Community Features
          </h2>
          <p className="text-lg text-gray-600 dark:text-polar-400 text-center max-w-2xl">
            Everything you need to learn, grow, and connect with like-minded
            individuals
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <div
                key={index}
                className="flex flex-col gap-4 bg-white dark:bg-polar-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow border border-gray-200 dark:border-polar-700"
              >
                <div className="relative w-full h-48">
                  <Image
                    src={feature.image}
                    alt={feature.title}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="flex flex-col gap-3 p-6">
                  <div className="flex flex-row items-center gap-3">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                      <Icon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h3 className="text-xl font-semibold dark:text-white">
                      {feature.title}
                    </h3>
                  </div>
                  <p className="text-gray-600 dark:text-polar-400">
                    {feature.description}
                  </p>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

