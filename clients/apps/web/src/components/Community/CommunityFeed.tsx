'use client'

import Avatar from '@polar-sh/ui/components/atoms/Avatar'
import { Card, CardContent } from '@polar-sh/ui/components/atoms/Card'
import Button from '@polar-sh/ui/components/atoms/Button'
import TextArea from '@polar-sh/ui/components/atoms/TextArea'
import { Heart, MessageCircle, Share2, Pin } from 'lucide-react'
import { useState } from 'react'
import { mockPosts, mockCurrentUser } from './mockData'

type Post = {
  id: string
  author: {
    name: string
    avatar_url: string | null
    badge?: string
  }
  title?: string
  content: string
  category: string
  timeAgo: string
  likes: number
  comments: number
  isLiked: boolean
  isFeatured: boolean
  icon: React.ReactNode | null
}

export const CommunityFeed = () => {
  const [posts, setPosts] = useState<Post[]>(mockPosts)
  const [newPostContent, setNewPostContent] = useState('')

  const handleLike = (postId: string) => {
    setPosts((prev) =>
      prev.map((post) =>
        post.id === postId
          ? {
              ...post,
              likes: post.isLiked
                ? post.likes - 1
                : post.likes + 1,
              isLiked: !post.isLiked,
            }
          : post,
      ),
    )
  }

  const handlePost = () => {
    if (!newPostContent.trim()) return

    const newPost: Post = {
      id: `post-${Date.now()}`,
      author: mockCurrentUser,
      content: newPostContent,
      category: 'General Discussion',
      timeAgo: 'just now',
      likes: 0,
      comments: 0,
      isLiked: false,
      isFeatured: false,
      icon: null,
    }

    setPosts([newPost, ...posts])
    setNewPostContent('')
  }

  return (
    <div className="flex flex-col gap-6">
      {/* Create Post */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-row gap-4">
            <Avatar
              name={mockCurrentUser.name}
              avatar_url={mockCurrentUser.avatar_url}
              className="h-10 w-10"
            />
            <div className="flex-1 flex flex-col gap-3">
              <TextArea
                placeholder={`What's on your mind, ${mockCurrentUser.name.split(' ')[0]}?`}
                value={newPostContent}
                onChange={(e) => setNewPostContent(e.target.value)}
                className="min-h-[80px] resize-none"
              />
              <div className="flex flex-row justify-end">
                <Button onClick={handlePost} disabled={!newPostContent.trim()}>
                  Post
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Featured Section */}
      {posts.some((p) => p.isFeatured) && (
        <div className="flex flex-col gap-4">
          <div className="flex flex-row items-center justify-between">
            <div className="flex flex-row items-center gap-2">
              <h3 className="text-lg font-semibold dark:text-white">
                Featured
              </h3>
              <div className="h-4 w-4 rounded-full bg-gray-300 dark:bg-polar-600 flex items-center justify-center">
                <span className="text-xs text-gray-600 dark:text-polar-300">
                  i
                </span>
              </div>
            </div>
          </div>
          {posts
            .filter((p) => p.isFeatured)
            .map((post) => (
              <PostCard key={post.id} post={post} onLike={handleLike} />
            ))}
        </div>
      )}

      {/* Regular Posts */}
      <div className="flex flex-col gap-4">
        {posts
          .filter((p) => !p.isFeatured)
          .map((post) => (
            <PostCard key={post.id} post={post} onLike={handleLike} />
          ))}
      </div>
    </div>
  )
}

interface PostCardProps {
  post: Post
  onLike: (postId: string) => void
}

const PostCard = ({ post, onLike }: PostCardProps) => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col gap-4">
          {/* Header */}
          <div className="flex flex-row items-start justify-between">
            <div className="flex flex-row items-center gap-3">
              <div className="relative">
                <Avatar
                  name={post.author.name}
                  avatar_url={post.author.avatar_url}
                  className="h-10 w-10"
                />
                {post.author.badge && (
                  <div className="absolute -bottom-1 -right-1 h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center border-2 border-white dark:border-polar-800">
                    <span className="text-xs text-white font-semibold">
                      {post.author.badge}
                    </span>
                  </div>
                )}
              </div>
              <div className="flex flex-col">
                <div className="flex flex-row items-center gap-2">
                  <span className="font-semibold dark:text-white">
                    {post.author.name}
                  </span>
                  {post.title && (
                    <>
                      {post.icon && (
                        <span className="text-gray-500 dark:text-polar-400">
                          {post.icon}
                        </span>
                      )}
                      <span className="font-medium dark:text-white">
                        {post.title}
                      </span>
                    </>
                  )}
                </div>
                <div className="flex flex-row items-center gap-2 text-sm text-gray-500 dark:text-polar-400">
                  <span>{post.timeAgo}</span>
                  <span>•</span>
                  <span>{post.category}</span>
                </div>
              </div>
            </div>
            {post.isFeatured && (
              <div className="flex items-center gap-1 text-yellow-500">
                <Pin className="h-4 w-4" />
              </div>
            )}
          </div>

          {/* Content */}
          <p className="text-gray-700 dark:text-polar-200 leading-relaxed">
            {post.content}
          </p>

          {/* Actions */}
          <div className="flex flex-row items-center gap-6 pt-2 border-t border-gray-200 dark:border-polar-700">
            <button
              onClick={() => onLike(post.id)}
              className="flex flex-row items-center gap-2 text-gray-600 dark:text-polar-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
            >
              <Heart
                className={`h-5 w-5 ${post.isLiked ? 'fill-red-500 text-red-500' : ''}`}
              />
              <span>{post.likes}</span>
            </button>
            <button className="flex flex-row items-center gap-2 text-gray-600 dark:text-polar-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors">
              <MessageCircle className="h-5 w-5" />
              <span>{post.comments}</span>
            </button>
            <button className="flex flex-row items-center gap-2 text-gray-600 dark:text-polar-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors">
              <Share2 className="h-5 w-5" />
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

