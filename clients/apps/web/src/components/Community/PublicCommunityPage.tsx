'use client'

import { schemas } from '@polar-sh/client'
import { PublicCommunityHero } from './PublicCommunityHero'
import { PublicCommunityContent } from './PublicCommunityContent'
import { PublicCommunityJoin } from './PublicCommunityJoin'

interface PublicCommunityPageProps {
  organization: schemas['Organization']
}

export const PublicCommunityPage = ({
  organization,
}: PublicCommunityPageProps) => {
  return (
    <div className="flex flex-col w-full min-h-screen bg-white dark:bg-polar-950">
      <PublicCommunityHero organization={organization} />
      <PublicCommunityContent organization={organization} />
      <PublicCommunityJoin organization={organization} />
    </div>
  )
}
