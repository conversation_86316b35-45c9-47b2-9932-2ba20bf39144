'use client'

import Button from '@polar-sh/ui/components/atoms/Button'
import { Card, CardContent } from '@polar-sh/ui/components/atoms/Card'
import { schemas } from '@polar-sh/client'
import { ArrowRight, CheckCircle2 } from 'lucide-react'
import Link from 'next/link'
import { mockPublicCommunities } from './mockPublicCommunities'

interface PublicCommunityJoinProps {
  organization: schemas['Organization']
}

const benefits = [
  'Acesso a conteúdo exclusivo e recursos',
  'Conexão direta com pessoas com interesses similares',
  'Sessões ao vivo semanais e Q&A',
  'Fóruns de discussão privados',
  'Acesso antecipado a novas funcionalidades',
  'Oportunidades de networking',
]

export const PublicCommunityJoin = ({
  organization,
}: PublicCommunityJoinProps) => {
  const mockCommunity = mockPublicCommunities.find(
    (c) => c.slug === organization.slug,
  )

  return (
    <div className="w-full bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 py-16 md:py-24">
      <div className="max-w-5xl mx-auto px-4 md:px-8">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          {/* Left: Benefits */}
          <div className="flex flex-col gap-6">
            <h2 className="text-3xl md:text-4xl font-bold text-white">
              O que você vai receber
            </h2>
            <div className="flex flex-col gap-3">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="flex flex-row items-start gap-3"
                >
                  <CheckCircle2 className="h-6 w-6 text-white shrink-0 mt-0.5" />
                  <span className="text-white/95 text-lg">{benefit}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Right: CTA Card */}
          <div className="flex flex-col gap-6">
            <Card className="bg-white dark:bg-polar-800 shadow-2xl">
              <CardContent className="p-8">
                <div className="flex flex-col gap-6">
                  <div className="flex flex-col gap-2">
                    <h3 className="text-2xl font-bold dark:text-white">
                      Junte-se à comunidade
                    </h3>
                    <p className="text-gray-600 dark:text-polar-400">
                      Torne-se parte de uma comunidade exclusiva de aprendizes,
                      criadores e inovadores.
                    </p>
                  </div>

                  {mockCommunity && (
                    <div className="flex flex-col gap-2 py-4 border-y border-gray-200 dark:border-polar-700">
                      <div className="flex flex-row items-center justify-between">
                        <span className="text-gray-600 dark:text-polar-400">
                          Categoria
                        </span>
                        <span className="font-semibold dark:text-white">
                          {mockCommunity.category}
                        </span>
                      </div>
                      <div className="flex flex-row items-center justify-between">
                        <span className="text-gray-600 dark:text-polar-400">
                          Membros
                        </span>
                        <span className="font-semibold dark:text-white">
                          {mockCommunity.memberCount.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  )}

                  <div className="flex flex-col gap-3">
                    <Link href={`/${organization.slug}/portal/authenticate`}>
                      <Button
                        size="lg"
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        <span>Entrar na Comunidade</span>
                        <ArrowRight className="h-5 w-5" />
                      </Button>
                    </Link>
                    <Link href={`/${organization.slug}`}>
                      <Button
                        size="lg"
                        variant="outline"
                        className="w-full border-gray-300 dark:border-polar-700"
                      >
                        Ver Mais Informações
                      </Button>
                    </Link>
                  </div>

                  <p className="text-xs text-gray-500 dark:text-polar-500 text-center">
                    Acesso instantâneo após o pagamento
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

