'use client'

import { useState } from 'react'
import Avatar from '@polar-sh/ui/components/atoms/Avatar'
import { Card, CardContent } from '@polar-sh/ui/components/atoms/Card'
import Input from '@polar-sh/ui/components/atoms/Input'
import { Users, Lock, Search, Palette, Music, DollarSign, Sparkles, Laptop, Carrot, Trophy, GraduationCap } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { mockPublicCommunities } from './mockPublicCommunities'

const categories = [
  { id: 'all', label: 'Todas', icon: null },
  { id: 'hobbies', label: 'Hobbies', icon: Palette },
  { id: 'music', label: 'Música', icon: Music },
  { id: 'finances', label: 'Finanças', icon: DollarSign },
  { id: 'spirituality', label: 'Espiritualidade', icon: Sparkles },
  { id: 'tech', label: 'Tech', icon: Laptop },
  { id: 'health', label: '<PERSON>úde', icon: Carrot },
  { id: 'sports', label: 'Esportes', icon: Trophy },
  { id: 'self-improvement', label: 'Desenvolvimento', icon: GraduationCap },
]

export const PublicCommunitiesList = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  const filteredCommunities = mockPublicCommunities.filter((community) => {
    const matchesSearch = 
      community.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      community.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      community.category.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesCategory = 
      selectedCategory === 'all' || 
      community.category.toLowerCase().includes(selectedCategory.toLowerCase())
    
    return matchesSearch && matchesCategory
  })

  return (
    <div className="w-full bg-white dark:bg-polar-950">
      {/* Hero Section */}
      <div className="w-full bg-white dark:bg-polar-950 py-12 md:py-20">
        <div className="max-w-4xl mx-auto px-4 md:px-8">
          {/* Title */}
          <div className="text-center mb-8">
            <h1 className="text-5xl md:text-6xl font-bold dark:text-white mb-4">
              Descubra comunidades
            </h1>
            <p className="text-lg md:text-xl text-gray-600 dark:text-polar-400">
              ou{' '}
              <Link 
                href="/signup" 
                className="text-blue-600 dark:text-blue-400 hover:underline font-medium"
              >
                crie a sua própria
              </Link>
            </p>
          </div>

          {/* Search Bar */}
          <div className="mb-8">
            <div className="relative">
              <Input
                type="search"
                placeholder="Busque por qualquer coisa..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full h-14 md:h-16 text-lg md:text-xl pl-14 pr-4"
                preSlot={<Search className="h-5 w-5 md:h-6 md:w-6" />}
              />
            </div>
          </div>

          {/* Category Filters */}
          <div className="flex flex-row items-center gap-2 flex-wrap md:flex-nowrap md:overflow-x-auto pb-2 md:scrollbar-hide">
            {categories.map((category) => {
              const Icon = category.icon
              const isActive = selectedCategory === category.id
              
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex flex-row items-center gap-2 px-4 py-2 rounded-full text-sm md:text-base font-medium whitespace-nowrap transition-colors ${
                    isActive
                      ? 'bg-blue-600 text-white dark:bg-blue-500'
                      : 'bg-gray-100 dark:bg-polar-800 text-gray-700 dark:text-polar-300 hover:bg-gray-200 dark:hover:bg-polar-700'
                  }`}
                >
                  {Icon && <Icon className="h-4 w-4" />}
                  <span>{category.label}</span>
                </button>
              )
            })}
          </div>
        </div>
      </div>

      {/* Communities Grid */}
      <div className="w-full bg-white dark:bg-polar-950 py-12 md:py-16">
        <div className="max-w-7xl mx-auto px-4 md:px-8">
          {/* Communities Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {filteredCommunities.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <p className="text-gray-600 dark:text-polar-400 text-lg">
                  Nenhuma comunidade encontrada. Tente outra busca ou categoria.
                </p>
              </div>
            ) : (
              filteredCommunities.map((community) => (
                <Link
                  key={community.slug}
                  href={`/${community.slug}/community`}
                  className="group"
                >
                  <Card className="bg-white dark:bg-polar-800 border border-gray-200 dark:border-polar-700 hover:shadow-xl transition-all duration-300 h-full flex flex-col overflow-hidden">
                    {/* Cover Image */}
                    <div className="relative w-full h-48 overflow-hidden">
                      {community.coverImage ? (
                        <Image
                          src={community.coverImage}
                          alt={`${community.name} cover`}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500" />
                      )}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                      
                      {/* Avatar */}
                      <div className="absolute bottom-4 left-4">
                        <div className="relative h-16 w-16 rounded-xl overflow-hidden border-4 border-white dark:border-polar-800 bg-white dark:bg-polar-800 shadow-lg">
                          <Avatar
                            name={community.name}
                            avatar_url={community.avatar_url ?? null}
                            className="h-full w-full"
                          />
                        </div>
                      </div>
                    </div>

                    <CardContent className="p-6 flex-1 flex flex-col">
                      {/* Community Info */}
                      <div className="flex flex-col gap-3 mb-4">
                        <h3 className="text-xl font-bold dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                          {community.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-polar-400 line-clamp-2">
                          {community.description}
                        </p>
                      </div>

                      {/* Stats */}
                      <div className="flex flex-row items-center gap-4 pt-4 border-t border-gray-200 dark:border-polar-700 mt-auto">
                        <div className="flex flex-row items-center gap-2 text-gray-600 dark:text-polar-400">
                          <Users className="h-4 w-4" />
                          <span className="text-sm font-medium">
                            {community.memberCount.toLocaleString()} membros
                          </span>
                        </div>
                        <div className="h-4 w-px bg-gray-300 dark:bg-polar-600" />
                        <div className="flex flex-row items-center gap-2 text-gray-600 dark:text-polar-400">
                          <Lock className="h-4 w-4" />
                          <span className="text-sm">Privado</span>
                        </div>
                      </div>

                      {/* Category Badge */}
                      <div className="mt-4">
                        <span className="inline-block px-3 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full">
                          {community.category}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))
            )}
          </div>

          {/* CTA Section */}
          <div className="mt-16 text-center">
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-polar-800 dark:to-polar-700 rounded-2xl p-8 md:p-12">
              <h2 className="text-2xl md:text-3xl font-bold dark:text-white mb-4">
                Quer criar sua própria comunidade?
              </h2>
              <p className="text-gray-600 dark:text-polar-400 mb-6 max-w-xl mx-auto">
                Junte-se a milhares de creators que já estão usando a Fluu para criar e gerenciar suas comunidades
              </p>
              <Link
                href="/signup"
                className="inline-block"
              >
                <button className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors">
                  Começar Agora
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
