# Plano e Modelo - Área de Membros/Comunidade

## Visão Geral
Área de membros estilo <PERSON>, Whop, Go High Level - uma plataforma de comunidade completa com feed de posts, navegação por seções, perfis de membros e interações sociais.

## Estrutura Implementada

### 1. Rotas e Navegação
- **Rota Principal**: `/dashboard/[organization]/community`
- **Navegação Global**: Adicionada ao menu lateral do dashboard com ícone `GroupsOutlined`
- **Layout**: Utiliza `DashboardBody` com layout responsivo

### 2. Componentes Principais

#### `CommunityPage`
- Componente principal que orquestra toda a página
- Layout com 3 colunas:
  - **Esquerda**: Navegação da comunidade (desktop apenas)
  - **Centro**: Conteúdo principal (feed, tabs, header)
  - **Direita**: Sidebar com informações da comunidade

#### `CommunityHeader`
- Header com avatar e nome da organização
- Barra de busca global
- Indicador de grupo privado

#### `CommunityNavigation`
- Sidebar de navegação lateral esquerda
- Seções:
  - **Home** (ativo, destaque amarelo)
  - **Start Here** (com ícone de pin)
  - **Introductions** (com ícone de mão)
  - **General Discussion** (com ícone de mensagem)

#### `CommunityTabs`
- Tabs principais no topo do feed:
  - Discussion (padrão)
  - Learning
  - Events
  - Leaderboard
  - Members
  - About

#### `CommunityFeed`
- Feed de posts com:
  - **Criação de Post**: Campo "What's on your mind" com textarea
  - **Posts Featured**: Seção destacada no topo
  - **Posts Regulares**: Feed principal
  - **Interações**: Like, comentários, compartilhar
  - **Badges de Membros**: Indicadores visuais (ex: "3", "1")
  - **Categorias**: Introductions, General Discussion, etc.

#### `CommunitySidebar`
- Sidebar direita com:
  - **Banner da Comunidade**: Imagem com nome da organização
  - **Botões de Ação**: VIDEOS, COMMUNITY, TOOLS
  - **Informações**: Nome, tipo (Private Group), descrição
  - **Quick Links**: Links rápidos com ícones
  - **Estatísticas**: Membros, Posts, Admins
  - **Avatares de Membros**: Grid de avatares
  - **Botão de Convite**: "INVITE MEMBERS"

### 3. Mock Data

#### `mockData.ts`
- `mockCurrentUser`: Usuário atual
- `mockPosts`: Array de posts com:
  - Autor (nome, avatar, badge)
  - Título e conteúdo
  - Categoria e tempo
  - Likes, comentários, featured status
  - Ícones contextuais
- `mockCommunityStats`: Estatísticas da comunidade
- `mockQuickLinks`: Links rápidos
- `mockMembers`: Lista de membros com avatares

## Padrões de Design Seguidos

### Componentes UI Utilizados
- `@polar-sh/ui/components/atoms/Card` - Cards para posts e seções
- `@polar-sh/ui/components/atoms/Avatar` - Avatares de usuários
- `@polar-sh/ui/components/atoms/Button` - Botões de ação
- `@polar-sh/ui/components/atoms/Input` - Inputs de busca
- `@polar-sh/ui/components/atoms/TextArea` - Criação de posts
- `@polar-sh/ui/components/atoms/Tabs` - Navegação por tabs
- `lucide-react` - Ícones

### Estilos
- **Dark Mode**: Suporte completo com classes `dark:`
- **Responsivo**: Layout adaptável com breakpoints
- **Cores**: 
  - Amarelo para elementos ativos/destaque
  - Azul para ações e links
  - Cinza para textos secundários

## Funcionalidades Implementadas

### ✅ Completas
1. ✅ Estrutura de rotas e layout
2. ✅ Navegação lateral da comunidade
3. ✅ Feed de posts com cards
4. ✅ Sistema de tabs principais
5. ✅ Sidebar direita com informações
6. ✅ Criação de posts
7. ✅ Mock data completo
8. ✅ Sistema de likes (interativo)
9. ✅ Badges de membros
10. ✅ Posts featured
11. ✅ Categorias de posts

### 🔄 Próximas Implementações (Futuro)
- [ ] Integração com backend/API
- [ ] Sistema de comentários completo
- [ ] Notificações em tempo real
- [ ] Busca funcional
- [ ] Filtros por categoria
- [ ] Paginação de posts
- [ ] Perfis de membros detalhados
- [ ] Sistema de eventos
- [ ] Leaderboard funcional
- [ ] Área de aprendizado
- [ ] Upload de imagens em posts
- [ ] Menções (@username)
- [ ] Hashtags

## Estrutura de Arquivos

```
src/components/Community/
├── CommunityPage.tsx          # Componente principal
├── CommunityHeader.tsx        # Header com busca
├── CommunityNavigation.tsx    # Sidebar esquerda
├── CommunityTabs.tsx         # Tabs principais
├── CommunityFeed.tsx         # Feed de posts
├── CommunitySidebar.tsx      # Sidebar direita
├── mockData.ts               # Dados mockados
└── PLANO_COMUNIDADE.md       # Este documento
```

## Notas de Implementação

1. **Componentes Reutilizáveis**: Todos os componentes seguem o padrão do projeto, usando componentes do `@polar-sh/ui`

2. **Mock Data**: Dados mockados estão em `mockData.ts` para fácil substituição por dados reais

3. **Responsividade**: 
   - Navegação lateral escondida em mobile (`hidden lg:block`)
   - Layout flexível que se adapta a diferentes tamanhos de tela

4. **Acessibilidade**: 
   - Uso de componentes semânticos
   - Suporte a navegação por teclado
   - Contraste adequado para dark mode

5. **Performance**:
   - Componentes client-side apenas onde necessário
   - Lazy loading pode ser adicionado para posts

## Exemplo de Uso

```tsx
// A rota é acessível em:
/dashboard/[organization-slug]/community

// O componente principal gerencia todo o estado e layout
<CommunityPage organization={organization} />
```

## Próximos Passos

1. **Backend Integration**: Conectar com API real
2. **Real-time Updates**: Implementar WebSockets ou SSE
3. **Rich Text Editor**: Adicionar editor de texto rico para posts
4. **Media Upload**: Sistema de upload de imagens/vídeos
5. **Moderation**: Ferramentas de moderação de conteúdo
6. **Analytics**: Métricas de engajamento da comunidade

