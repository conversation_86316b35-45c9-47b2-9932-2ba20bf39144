'use client'

import Avatar from '@polar-sh/ui/components/atoms/Avatar'
import { Card, CardContent } from '@polar-sh/ui/components/atoms/Card'
import { Heart, MessageCircle, Share2, Pin, Calendar } from 'lucide-react'
import Image from 'next/image'

const previewPosts = [
  {
    id: '1',
    author: {
      name: '<PERSON>',
      avatar_url: null,
      badge: '3',
    },
    title: 'Welcome New Members!',
    content:
      "Excited to welcome all our new members this week! We've got some amazing content coming up, including a live Q&A session this Friday. Make sure to check out the pinned resources in the Start Here section.",
    category: 'Announcements',
    timeAgo: '2 hours ago',
    likes: 24,
    comments: 8,
    isFeatured: true,
    icon: <Pin className="h-4 w-4" />,
    image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&q=80',
  },
  {
    id: '2',
    author: {
      name: '<PERSON>',
      avatar_url: null,
      badge: '2',
    },
    title: 'Weekly Group Coaching Session',
    content:
      "This week's group coaching call is scheduled for Thursday at 3 PM EST. We'll be covering advanced strategies and answering your questions. See you there!",
    category: 'Events',
    timeAgo: '5 hours ago',
    likes: 18,
    comments: 5,
    isFeatured: false,
    icon: <Calendar className="h-4 w-4" />,
  },
  {
    id: '3',
    author: {
      name: 'Emma Rodriguez',
      avatar_url: null,
      badge: '1',
    },
    title: 'New Learning Module Available',
    content:
      'Just released a new comprehensive module on advanced techniques. Check it out in the Learning section. Would love to hear your feedback!',
    category: 'Learning',
    timeAgo: '1 day ago',
    likes: 32,
    comments: 12,
    isFeatured: false,
    icon: null,
  },
]

export const PublicCommunityPreview = () => {
  return (
    <div className="w-full bg-gray-50 dark:bg-polar-950 py-16 md:py-24">
      <div className="max-w-4xl mx-auto px-4 md:px-8">
        <div className="flex flex-col items-center gap-4 mb-12">
          <h2 className="text-3xl md:text-4xl font-bold dark:text-white text-center">
            See What's Happening
          </h2>
          <p className="text-lg text-gray-600 dark:text-polar-400 text-center max-w-2xl">
            Get a preview of the discussions, events, and content happening in
            our community
          </p>
        </div>

        <div className="flex flex-col gap-6">
          {previewPosts.map((post) => (
            <Card
              key={post.id}
              className="bg-white dark:bg-polar-800 border border-gray-200 dark:border-polar-700"
            >
              <CardContent className="p-6">
                <div className="flex flex-col gap-4">
                  {/* Header */}
                  <div className="flex flex-row items-start justify-between">
                    <div className="flex flex-row items-center gap-3">
                      <div className="relative">
                        <Avatar
                          name={post.author.name}
                          avatar_url={post.author.avatar_url}
                          className="h-10 w-10"
                        />
                        {post.author.badge && (
                          <div className="absolute -bottom-1 -right-1 h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center border-2 border-white dark:border-polar-800">
                            <span className="text-xs text-white font-semibold">
                              {post.author.badge}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col">
                        <div className="flex flex-row items-center gap-2 flex-wrap">
                          <span className="font-semibold dark:text-white">
                            {post.author.name}
                          </span>
                          {post.title && (
                            <>
                              {post.icon && (
                                <span className="text-gray-500 dark:text-polar-400">
                                  {post.icon}
                                </span>
                              )}
                              <span className="font-medium dark:text-white">
                                {post.title}
                              </span>
                            </>
                          )}
                        </div>
                        <div className="flex flex-row items-center gap-2 text-sm text-gray-500 dark:text-polar-400">
                          <span>{post.timeAgo}</span>
                          <span>•</span>
                          <span>{post.category}</span>
                        </div>
                      </div>
                    </div>
                    {post.isFeatured && (
                      <div className="flex items-center gap-1 text-yellow-500">
                        <Pin className="h-4 w-4" />
                      </div>
                    )}
                  </div>

                  {/* Image */}
                  {post.image && (
                    <div className="relative w-full h-64 rounded-lg overflow-hidden">
                      <Image
                        src={post.image}
                        alt={post.title || 'Post image'}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}

                  {/* Content */}
                  <p className="text-gray-700 dark:text-polar-200 leading-relaxed">
                    {post.content}
                  </p>

                  {/* Actions */}
                  <div className="flex flex-row items-center gap-6 pt-2 border-t border-gray-200 dark:border-polar-700">
                    <div className="flex flex-row items-center gap-2 text-gray-600 dark:text-polar-400">
                      <Heart className="h-5 w-5" />
                      <span>{post.likes}</span>
                    </div>
                    <div className="flex flex-row items-center gap-2 text-gray-600 dark:text-polar-400">
                      <MessageCircle className="h-5 w-5" />
                      <span>{post.comments}</span>
                    </div>
                    <div className="flex flex-row items-center gap-2 text-gray-600 dark:text-polar-400">
                      <Share2 className="h-5 w-5" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-8 text-center">
          <p className="text-gray-600 dark:text-polar-400">
            Join the community to see all posts and participate in discussions
          </p>
        </div>
      </div>
    </div>
  )
}

