'use client'

import Avatar from '@polar-sh/ui/components/atoms/Avatar'
import { Card, CardContent } from '@polar-sh/ui/components/atoms/Card'
import { Star, Quote } from 'lucide-react'
import { schemas } from '@polar-sh/client'

interface PublicCommunityTestimonialsProps {
  organization?: schemas['Organization']
}

const defaultTestimonials = [
  {
    name: '<PERSON>',
    role: 'Entrepreneur',
    avatar_url: null,
    content:
      "This community has been a game-changer for me. The support and knowledge sharing here is incredible. I've learned so much and made valuable connections.",
    rating: 5,
  },
  {
    name: '<PERSON>',
    role: 'Content Creator',
    avatar_url: null,
    content:
      'The best investment I made this year. The resources, events, and community support have helped me grow my business significantly.',
    rating: 5,
  },
  {
    name: '<PERSON>',
    role: 'Developer',
    avatar_url: null,
    content:
      'Amazing community with active members who are always willing to help. The weekly sessions are incredibly valuable and the content is top-notch.',
    rating: 5,
  },
  {
    name: '<PERSON>',
    role: 'Marketing Professional',
    avatar_url: null,
    content:
      "I've been part of many communities, but this one stands out. The quality of discussions and the genuine support from members is unmatched.",
    rating: 5,
  },
]

const dietaDaSelvaTestimonials = [
  {
    name: 'Ana Paula',
    role: '29 anos - São Paulo',
    avatar_url: null,
    content:
      'Em 3 meses perdi 12kg e ganhei uma energia que não tinha há anos! A comunidade é incrível! As receitas são deliciosas e o suporte do Dr. Rafael é excepcional.',
    rating: 5,
  },
  {
    name: 'Carlos Mendes',
    role: '45 anos - Rio de Janeiro',
    avatar_url: null,
    content:
      'Minha pressão normalizou e parei de tomar remédios. A alimentação natural mudou minha vida! A Dieta da Selva me ensinou que comida de verdade é remédio.',
    rating: 5,
  },
  {
    name: 'Juliana Costa',
    role: '35 anos - Belo Horizonte',
    avatar_url: null,
    content:
      'As receitas são deliciosas e fáceis. Minha família toda adorou! Meus filhos comem melhor agora e eu me sinto mais disposta e saudável do que nunca.',
    rating: 5,
  },
  {
    name: 'Roberto Alves',
    role: '52 anos - Brasília',
    avatar_url: null,
    content:
      'Aos 52 anos me sinto melhor que aos 30. A Dieta da Selva me deu uma nova vida! Perdi peso, ganhei energia e minha saúde melhorou drasticamente.',
    rating: 5,
  },
]

const getTestimonials = (slug?: string) => {
  if (slug === 'dieta-da-selva') {
    return dietaDaSelvaTestimonials
  }
  return defaultTestimonials
}

export const PublicCommunityTestimonials = ({
  organization,
}: PublicCommunityTestimonialsProps = {}) => {
  const testimonials = getTestimonials(organization?.slug)
  return (
    <div className="w-full bg-white dark:bg-polar-900 py-16 md:py-24">
      <div className="max-w-6xl mx-auto px-4 md:px-8">
        <div className="flex flex-col items-center gap-4 mb-12">
          <h2 className="text-3xl md:text-4xl font-bold dark:text-white text-center">
            {organization?.slug === 'dieta-da-selva' ? 'O que nossos membros dizem' : 'What Members Say'}
          </h2>
          <p className="text-lg text-gray-600 dark:text-polar-400 text-center max-w-2xl">
            {organization?.slug === 'dieta-da-selva'
              ? 'Junte-se a milhares de pessoas que transformaram suas vidas através da alimentação natural'
              : 'Join thousands of satisfied members who are growing and learning together'}
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {testimonials.map((testimonial, index) => (
            <Card
              key={index}
              className="bg-white dark:bg-polar-800 border border-gray-200 dark:border-polar-700"
            >
              <CardContent className="p-6">
                <div className="flex flex-col gap-4">
                  <div className="flex flex-row items-start gap-3">
                    <Quote className="h-6 w-6 text-blue-500 shrink-0 mt-1" />
                    <div className="flex flex-col gap-2 flex-1">
                      <p className="text-gray-700 dark:text-polar-200 leading-relaxed">
                        {testimonial.content}
                      </p>
                      <div className="flex flex-row items-center gap-1">
                        {Array.from({ length: testimonial.rating }).map(
                          (_, i) => (
                            <Star
                              key={i}
                              className="h-4 w-4 fill-yellow-400 text-yellow-400"
                            />
                          ),
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-row items-center gap-3 pt-4 border-t border-gray-200 dark:border-polar-700">
                    <Avatar
                      name={testimonial.name}
                      avatar_url={testimonial.avatar_url}
                      className="h-10 w-10"
                    />
                    <div className="flex flex-col">
                      <span className="font-semibold dark:text-white">
                        {testimonial.name}
                      </span>
                      <span className="text-sm text-gray-600 dark:text-polar-400">
                        {testimonial.role}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

