'use client'

import { Card, CardContent } from '@polar-sh/ui/components/atoms/Card'
import Button from '@polar-sh/ui/components/atoms/Button'
import { Home, Pin, Hand, MessageSquare, ChevronUp } from 'lucide-react'
import { useState } from 'react'
import { twMerge } from 'tailwind-merge'

const communityNavItems = [
  {
    id: 'home',
    label: 'Home',
    icon: Home,
    isActive: true,
  },
  {
    id: 'start-here',
    label: 'Start Here',
    icon: Pin,
    isActive: false,
  },
  {
    id: 'introductions',
    label: 'Introductions',
    icon: Hand,
    isActive: false,
  },
  {
    id: 'general-discussion',
    label: 'General Discussion',
    icon: MessageSquare,
    isActive: false,
  },
]

export const CommunityNavigation = () => {
  const [activeItem, setActiveItem] = useState('home')

  return (
    <Card className="dark:bg-polar-800 bg-gray-50 border-transparent">
      <CardContent className="p-4">
        <div className="flex flex-col gap-2">
          <div className="flex flex-row items-center justify-end mb-2">
            <ChevronUp className="h-4 w-4 text-gray-500 dark:text-polar-400 cursor-pointer" />
          </div>
          {communityNavItems.map((item) => {
            const Icon = item.icon
            const isActive = activeItem === item.id
            return (
              <button
                key={item.id}
                onClick={() => setActiveItem(item.id)}
                className={twMerge(
                  'w-full flex flex-row items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                  isActive
                    ? 'bg-yellow-500 hover:bg-yellow-600 text-white'
                    : 'bg-transparent text-gray-700 dark:text-polar-300 hover:bg-gray-100 dark:hover:bg-polar-700',
                )}
              >
                <Icon className="h-4 w-4" />
                <span>{item.label}</span>
              </button>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}

