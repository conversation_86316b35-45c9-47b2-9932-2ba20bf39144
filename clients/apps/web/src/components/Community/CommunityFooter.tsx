'use client'

import Link from 'next/link'
import { PropsWithChildren } from 'react'
import { twMerge } from 'tailwind-merge'
import { PolarLogotype } from '../Layout/Public/PolarLogotype'
import { mockPublicCommunities } from './mockPublicCommunities'
import { Twitter, Github, Mail } from 'lucide-react'

const Footer = () => {
  return (
    <footer className="w-full border-t border-gray-200 dark:border-polar-700 bg-white dark:bg-polar-900">
      <div className="max-w-7xl mx-auto px-4 md:px-8 py-12 md:py-16">
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 md:gap-12">
          {/* Brand */}
          <div className="col-span-2 md:col-span-1 lg:col-span-2 flex flex-col gap-4">
            <PolarLogotype logoVariant="icon" size={32} />
            <p className="text-sm text-gray-600 dark:text-polar-400 max-w-xs">
              A plataforma completa para creators brasileiros venderem produtos
              digitais, comunidades e assinaturas.
            </p>
            <div className="flex flex-row items-center gap-4">
              <FooterLink
                href="https://x.com/fluu_br"
                target="_blank"
                className="p-2 hover:bg-gray-100 dark:hover:bg-polar-800 rounded-lg transition-colors"
              >
                <Twitter className="h-5 w-5 text-gray-600 dark:text-polar-400" />
              </FooterLink>
              <FooterLink
                href="https://github.com/fluu"
                target="_blank"
                className="p-2 hover:bg-gray-100 dark:hover:bg-polar-800 rounded-lg transition-colors"
              >
                <Github className="h-5 w-5 text-gray-600 dark:text-polar-400" />
              </FooterLink>
              <FooterLink
                href="mailto:<EMAIL>"
                className="p-2 hover:bg-gray-100 dark:hover:bg-polar-800 rounded-lg transition-colors"
              >
                <Mail className="h-5 w-5 text-gray-600 dark:text-polar-400" />
              </FooterLink>
            </div>
          </div>

          {/* Product */}
          <div className="flex flex-col gap-4">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
              Produto
            </h3>
            <div className="flex flex-col gap-2">
              <FooterLink href="/features/products">Produtos Digitais</FooterLink>
              <FooterLink href="/features/finance">PIX e Pagamentos</FooterLink>
              <FooterLink href="/features/usage-billing">Assinaturas</FooterLink>
              <FooterLink href="/features/benefits">Entregas Automáticas</FooterLink>
              <FooterLink href="/resources/pricing">Preços</FooterLink>
            </div>
          </div>

          {/* Communities */}
          <div className="flex flex-col gap-4">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
              Comunidades
            </h3>
            <div className="flex flex-col gap-2">
              {mockPublicCommunities.slice(0, 5).map((community) => (
                <FooterLink
                  key={community.slug}
                  href={`/${community.slug}/community`}
                >
                  {community.name}
                </FooterLink>
              ))}
            </div>
          </div>

          {/* Company */}
          <div className="flex flex-col gap-4">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
              Empresa
            </h3>
            <div className="flex flex-col gap-2">
              <FooterLink href="/company">Sobre a Fluu</FooterLink>
              <FooterLink href="/login">Começar Agora</FooterLink>
              <FooterLink href="/legal/terms">Termos de Serviço</FooterLink>
              <FooterLink href="/legal/privacy">Política de Privacidade</FooterLink>
            </div>
          </div>

          {/* Support */}
          <div className="flex flex-col gap-4">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
              Suporte
            </h3>
            <div className="flex flex-col gap-2">
              <FooterLink href="mailto:<EMAIL>">
                <EMAIL>
              </FooterLink>
              <FooterLink href="https://discord.gg/fluu" target="_blank">
                Discord
              </FooterLink>
              <FooterLink href="/status">Status do Sistema</FooterLink>
            </div>
          </div>
        </div>

        {/* Bottom */}
        <div className="mt-12 pt-8 border-t border-gray-200 dark:border-polar-700 flex flex-col md:flex-row items-center justify-between gap-4">
          <p className="text-sm text-gray-600 dark:text-polar-400">
            &copy; {new Date().getFullYear()} Fluu. Todos os direitos reservados.
          </p>
          <div className="flex flex-row items-center gap-6 text-sm text-gray-600 dark:text-polar-400">
            <FooterLink href="/legal/privacy" className="text-sm">
              Privacidade
            </FooterLink>
            <FooterLink href="/legal/terms" className="text-sm">
              Termos
            </FooterLink>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer

const FooterLinkClassnames =
  'text-sm text-gray-600 dark:text-polar-400 hover:text-gray-900 dark:hover:text-white transition-colors'

const FooterLink = (
  props: PropsWithChildren<{
    href: string
    target?: string
    className?: string
  }>,
) => {
  const isExternal = props.href.toString().startsWith('http')
  const { target, href, className, ...restProps } = props

  if (isExternal) {
    return (
      <a
        className={twMerge(FooterLinkClassnames, className)}
        href={href}
        target={target || '_blank'}
        rel={target === '_blank' ? 'noopener noreferrer' : undefined}
        {...restProps}
      >
        {props.children}
      </a>
    )
  }

  return (
    <Link
      className={twMerge(FooterLinkClassnames, className)}
      href={href}
      {...restProps}
    >
      {props.children}
    </Link>
  )
}

