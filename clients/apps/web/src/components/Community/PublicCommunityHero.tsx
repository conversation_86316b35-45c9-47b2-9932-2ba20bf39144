'use client'

import Avatar from '@polar-sh/ui/components/atoms/Avatar'
import { schemas } from '@polar-sh/client'
import { Users, Lock, Star } from 'lucide-react'
import Image from 'next/image'
import { mockPublicCommunities } from './mockPublicCommunities'

interface PublicCommunityHeroProps {
  organization: schemas['Organization']
}

export const PublicCommunityHero = ({
  organization,
}: PublicCommunityHeroProps) => {
  const mockCommunity = mockPublicCommunities.find(
    (c) => c.slug === organization.slug,
  )

  const memberCount = mockCommunity?.memberCount || 1234

  return (
    <div className="relative w-full">
      {/* Cover Image */}
      <div className="relative w-full h-80 md:h-[500px] overflow-hidden bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500">
        {mockCommunity?.coverImage ? (
          <Image
            src={mockCommunity.coverImage}
            alt={`${organization.name} Community Cover`}
            fill
            className="object-cover"
            priority
          />
        ) : (
          <Image
            src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=1920&q=80"
            alt={`${organization.name} Community Cover`}
            fill
            className="object-cover"
            priority
          />
        )}
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />
      </div>

      {/* Content */}
      <div className="relative -mt-20 md:-mt-32 px-4 md:px-8">
        <div className="max-w-5xl mx-auto">
          <div className="flex flex-col md:flex-row items-start md:items-end gap-6">
            {/* Avatar */}
            <div className="relative shrink-0">
              <div className="relative h-28 w-28 md:h-36 md:w-36 rounded-2xl overflow-hidden border-4 border-white dark:border-polar-900 bg-white dark:bg-polar-800 shadow-2xl">
                <Avatar
                  name={organization.name}
                  avatar_url={organization.avatar_url}
                  className="h-full w-full"
                />
              </div>
              <div className="absolute -bottom-2 -right-2 h-7 w-7 rounded-full bg-green-500 border-4 border-white dark:border-polar-900 flex items-center justify-center shadow-lg">
                <Lock className="h-3.5 w-3.5 text-white" />
              </div>
            </div>

            {/* Info */}
            <div className="flex-1 flex flex-col gap-4 pb-2">
              <div className="flex flex-col gap-2">
                <h1 className="text-3xl md:text-5xl font-bold text-white drop-shadow-lg">
                  {organization.name}
                </h1>
              </div>

              <div className="flex flex-row items-center gap-6 flex-wrap">
                <div className="flex flex-row items-center gap-2 text-white/95">
                  <Users className="h-5 w-5" />
                  <span className="font-semibold">
                    {memberCount.toLocaleString()} membros
                  </span>
                </div>
                <div className="h-5 w-px bg-white/30" />
                <div className="flex flex-row items-center gap-2 text-white/95">
                  <Lock className="h-5 w-5" />
                  <span className="font-semibold">Grupo Privado</span>
                </div>
                <div className="h-5 w-px bg-white/30" />
                <div className="flex flex-row items-center gap-1 text-white/95">
                  <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  <span className="font-semibold">4.9</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
