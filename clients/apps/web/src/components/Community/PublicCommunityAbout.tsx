'use client'

import { schemas } from '@polar-sh/client'
import Image from 'next/image'
import { CheckCircle2 } from 'lucide-react'

interface PublicCommunityAboutProps {
  organization: schemas['Organization']
}

const benefits = [
  'Access to exclusive content and resources',
  'Direct connection with like-minded individuals',
  'Weekly live sessions and Q&A',
  'Private discussion forums',
  'Early access to new features and updates',
  'Networking opportunities',
]

export const PublicCommunityAbout = ({
  organization,
}: PublicCommunityAboutProps) => {
  return (
    <div className="w-full bg-gray-50 dark:bg-polar-950 py-16 md:py-24">
      <div className="max-w-6xl mx-auto px-4 md:px-8">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          {/* Image */}
          <div className="relative w-full h-96 rounded-2xl overflow-hidden shadow-xl">
            <Image
              src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&q=80"
              alt="Community members collaborating"
              fill
              className="object-cover"
            />
          </div>

          {/* Content */}
          <div className="flex flex-col gap-6">
            <div className="flex flex-col gap-4">
              <h2 className="text-3xl md:text-4xl font-bold dark:text-white">
                About {organization.name} Community
              </h2>
              <p className="text-lg text-gray-700 dark:text-polar-300 leading-relaxed">
                Welcome to the {organization.name} Community! This is a private
                space where members come together to share knowledge, support
                each other, and grow both personally and professionally.
              </p>
              <p className="text-lg text-gray-700 dark:text-polar-300 leading-relaxed">
                Our community is built on the principles of collaboration,
                respect, and continuous learning. Whether you're here to learn,
                teach, or simply connect with others who share your interests,
                you'll find a welcoming and supportive environment.
              </p>
            </div>

            <div className="flex flex-col gap-3">
              <h3 className="text-xl font-semibold dark:text-white">
                What You'll Get:
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {benefits.map((benefit, index) => (
                  <div
                    key={index}
                    className="flex flex-row items-start gap-2"
                  >
                    <CheckCircle2 className="h-5 w-5 text-green-500 mt-0.5 shrink-0" />
                    <span className="text-gray-700 dark:text-polar-300">
                      {benefit}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

