'use client'

import { PolarLogotype } from '@/components/Layout/Public/PolarLogotype'
import TopbarRight from '@/components/Layout/Public/TopbarRight'
import { schemas } from '@polar-sh/client'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { twMerge } from 'tailwind-merge'
import { AuthModal } from '@/components/Auth/AuthModal'
import { Modal } from '@/components/Modal'
import { useModal } from '@/components/Modal/useModal'
import Button from '@polar-sh/ui/components/atoms/Button'
import { usePostHog } from '@/hooks/posthog'

interface CommunityHeaderProps {
  organization: schemas['Organization']
  authenticatedUser?: schemas['UserRead']
}

export const CommunityHeader = ({
  organization,
  authenticatedUser,
}: CommunityHeaderProps) => {
  const pathname = usePathname()
  const posthog = usePostHog()
  const { isShown: isModalShown, hide: hideModal, show: showModal } = useModal()

  const onLoginClick = () => {
    posthog.capture('website:user:login:click')
    showModal()
  }

  const isCommunityPage = pathname?.includes('/community')

  return (
    <>
      <header className="sticky top-0 z-50 w-full border-b border-gray-200 dark:border-polar-700 bg-white/80 dark:bg-polar-900/80 backdrop-blur-md">
        <div className="max-w-7xl mx-auto px-4 md:px-8">
          <div className="flex flex-row items-center justify-between h-16 md:h-20">
            {/* Logo */}
            <Link href="/" className="flex items-center">
              <PolarLogotype logoVariant="icon" size={32} />
            </Link>

            {/* Navigation - Desktop */}
            <nav className="hidden md:flex flex-row items-center gap-8">
              <Link
                href={`/${organization.slug}`}
                className={twMerge(
                  'text-sm font-medium transition-colors',
                  !isCommunityPage
                    ? 'text-gray-900 dark:text-white'
                    : 'text-gray-600 dark:text-polar-400 hover:text-gray-900 dark:hover:text-white',
                )}
              >
                Loja
              </Link>
              <Link
                href={`/${organization.slug}/community`}
                className={twMerge(
                  'text-sm font-medium transition-colors',
                  isCommunityPage
                    ? 'text-gray-900 dark:text-white'
                    : 'text-gray-600 dark:text-polar-400 hover:text-gray-900 dark:hover:text-white',
                )}
              >
                Comunidade
              </Link>
              <Link
                href="/features/products"
                className="text-sm font-medium text-gray-600 dark:text-polar-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                Recursos
              </Link>
            </nav>

            {/* Right Side */}
            <div className="flex flex-row items-center gap-4">
              {authenticatedUser ? (
                <TopbarRight
                  authenticatedUser={authenticatedUser}
                  storefrontOrg={organization}
                />
              ) : (
                <>
                  <Button
                    onClick={onLoginClick}
                    variant="ghost"
                    className="hidden md:flex"
                  >
                    Entrar
                  </Button>
                  <Link href="/signup">
                    <Button size="sm">Começar</Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </header>

      <Modal
        title="Entrar"
        isShown={isModalShown}
        hide={hideModal}
        modalContent={<AuthModal />}
        className="lg:w-full lg:max-w-[480px]"
      />
    </>
  )
}
