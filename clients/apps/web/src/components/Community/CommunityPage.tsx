'use client'

import { DashboardBody } from '@/components/Layout/DashboardLayout'
import { schemas } from '@polar-sh/client'
import { CommunityFeed } from './CommunityFeed'
import { CommunityHeader } from './CommunityHeader'
import { CommunityNavigation } from './CommunityNavigation'
import { CommunitySidebar } from './CommunitySidebar'
import { CommunityTabs } from './CommunityTabs'

interface CommunityPageProps {
  organization: schemas['Organization']
}

export const CommunityPage = ({ organization }: CommunityPageProps) => {
  return (
    <DashboardBody
      title="Community"
      className="flex flex-col gap-6"
      wide
      contextView={<CommunitySidebar organization={organization} />}
      contextViewPlacement="right"
    >
      <div className="flex flex-row gap-6">
        {/* Left Navigation Sidebar */}
        <div className="hidden lg:block w-64 shrink-0">
          <CommunityNavigation />
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col gap-6 min-w-0">
          <CommunityHeader organization={organization} />
          <CommunityTabs />
          <CommunityFeed />
        </div>
      </div>
    </DashboardBody>
  )
}

