'use client'

import { Tabs, TabsList, TabsTrigger } from '@polar-sh/ui/components/atoms/Tabs'
import { MessageSquare, BookOpen, Calendar, Trophy, Users, Info } from 'lucide-react'
import { useState } from 'react'

const tabs = [
  { id: 'discussion', label: 'Discussion', icon: MessageSquare },
  { id: 'learning', label: 'Learning', icon: BookOpen },
  { id: 'events', label: 'Events', icon: Calendar },
  { id: 'leaderboard', label: 'Leaderboard', icon: Trophy },
  { id: 'members', label: 'Members', icon: Users },
  { id: 'about', label: 'About', icon: Info },
]

export const CommunityTabs = () => {
  const [activeTab, setActiveTab] = useState('discussion')

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab}>
      <TabsList className="flex flex-row bg-transparent ring-0 dark:bg-transparent dark:ring-0">
        {tabs.map((tab) => {
          const Icon = tab.icon
          return (
            <TabsTrigger
              key={tab.id}
              value={tab.id}
              className="flex flex-row items-center gap-x-2 px-4"
            >
              <Icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </TabsTrigger>
          )
        })}
      </TabsList>
    </Tabs>
  )
}

