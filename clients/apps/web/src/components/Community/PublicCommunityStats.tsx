'use client'

import { Users, MessageSquare, Calendar, TrendingUp } from 'lucide-react'

const stats = [
  {
    icon: Users,
    value: '1,234',
    label: 'Active Members',
    color: 'text-blue-500',
  },
  {
    icon: MessageSquare,
    value: '5,678',
    label: 'Total Posts',
    color: 'text-green-500',
  },
  {
    icon: Calendar,
    value: '42',
    label: 'Events This Month',
    color: 'text-purple-500',
  },
  {
    icon: TrendingUp,
    value: '98%',
    label: 'Engagement Rate',
    color: 'text-orange-500',
  },
]

export const PublicCommunityStats = () => {
  return (
    <div className="w-full bg-white dark:bg-polar-900 border-b border-gray-200 dark:border-polar-700">
      <div className="max-w-6xl mx-auto px-4 md:px-8 py-8">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <div
                key={index}
                className="flex flex-col items-center gap-2 text-center"
              >
                <div className={`${stat.color} mb-2`}>
                  <Icon className="h-8 w-8" />
                </div>
                <div className="text-2xl md:text-3xl font-bold dark:text-white">
                  {stat.value}
                </div>
                <div className="text-sm text-gray-600 dark:text-polar-400">
                  {stat.label}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

