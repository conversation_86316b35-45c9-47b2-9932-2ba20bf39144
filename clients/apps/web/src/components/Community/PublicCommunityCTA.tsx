'use client'

import Button from '@polar-sh/ui/components/atoms/Button'
import { schemas } from '@polar-sh/client'
import { ArrowRight, Lock, Users, Sparkles } from 'lucide-react'
import Link from 'next/link'

interface PublicCommunityCTAProps {
  organization: schemas['Organization']
}

export const PublicCommunityCTA = ({
  organization,
}: PublicCommunityCTAProps) => {
  return (
    <div className="w-full bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 py-16 md:py-24">
      <div className="max-w-4xl mx-auto px-4 md:px-8">
        <div className="flex flex-col items-center gap-8 text-center">
          <div className="flex flex-col gap-4">
            <h2 className="text-3xl md:text-4xl font-bold text-white">
              Ready to Join {organization.name} Community?
            </h2>
            <p className="text-lg text-white/90 max-w-2xl">
              Become part of an exclusive community of learners, creators, and
              innovators. Connect with like-minded individuals and unlock your
              full potential.
            </p>
          </div>

          <div className="flex flex-col md:flex-row items-center gap-4">
            <Link href={`/${organization.slug}/portal/authenticate`}>
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                <span>Join Community</span>
                <ArrowRight className="h-5 w-5" />
              </Button>
            </Link>
            <Link href={`/${organization.slug}`}>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white/10"
              >
                Learn More
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8 w-full max-w-3xl">
            <div className="flex flex-col items-center gap-2 p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
              <Lock className="h-8 w-8 text-white mb-2" />
              <h3 className="font-semibold text-white">Private & Secure</h3>
              <p className="text-sm text-white/80 text-center">
                Exclusive access for members only
              </p>
            </div>
            <div className="flex flex-col items-center gap-2 p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
              <Users className="h-8 w-8 text-white mb-2" />
              <h3 className="font-semibold text-white">Active Community</h3>
              <p className="text-sm text-white/80 text-center">
                Connect with 1,000+ members
              </p>
            </div>
            <div className="flex flex-col items-center gap-2 p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
              <Sparkles className="h-8 w-8 text-white mb-2" />
              <h3 className="font-semibold text-white">Premium Content</h3>
              <p className="text-sm text-white/80 text-center">
                Exclusive resources and events
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

