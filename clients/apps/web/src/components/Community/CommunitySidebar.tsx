'use client'

import Avatar from '@polar-sh/ui/components/atoms/Avatar'
import { Card, CardContent, CardHeader, CardTitle } from '@polar-sh/ui/components/atoms/Card'
import Button from '@polar-sh/ui/components/atoms/Button'
import { schemas } from '@polar-sh/client'
import { Lock, Link as LinkIcon, Users, FileText, Trophy } from 'lucide-react'
import { mockCommunityStats, mockQuickLinks, mockMembers } from './mockData'

interface CommunitySidebarProps {
  organization: schemas['Organization']
}

export const CommunitySidebar = ({ organization }: CommunitySidebarProps) => {
  return (
    <div className="flex flex-col gap-6">
      {/* Community Banner */}
      <Card className="overflow-hidden">
        <div className="relative h-32 bg-gradient-to-br from-blue-600 to-blue-800 flex items-center justify-center">
          <div className="absolute inset-0 bg-black/20" />
          <h2 className="relative text-white font-bold text-xl z-10">
            {organization.name.toUpperCase()}
          </h2>
        </div>
        <CardContent className="p-4">
          <div className="flex flex-row gap-2">
            <Button variant="outline" size="sm" className="flex-1">
              VIDEOS
            </Button>
            <Button variant="outline" size="sm" className="flex-1">
              COMMUNITY
            </Button>
            <Button variant="outline" size="sm" className="flex-1">
              TOOLS
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Community Info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">{organization.name}</CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col gap-4">
          <div className="flex flex-row items-center gap-2 text-sm text-gray-600 dark:text-polar-400">
            <Lock className="h-4 w-4" />
            <span>Private Group</span>
          </div>
          <p className="text-sm text-gray-600 dark:text-polar-400">
            The unofficial undisputed #1 {organization.name} community.
          </p>

          {/* Quick Links */}
          <div className="flex flex-col gap-2 pt-2 border-t border-gray-200 dark:border-polar-700">
            <h4 className="text-sm font-semibold dark:text-white">Quick Links</h4>
            {mockQuickLinks.map((link, index) => (
              <a
                key={index}
                href="#"
                className="flex flex-row items-center gap-2 text-sm text-gray-600 dark:text-polar-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
              >
                <LinkIcon className="h-4 w-4" />
                <span>{link}</span>
              </a>
            ))}
          </div>

          {/* Stats */}
          <div className="flex flex-col gap-2 pt-2 border-t border-gray-200 dark:border-polar-700">
            <div className="flex flex-row items-center gap-2 text-sm">
              <Users className="h-4 w-4 text-gray-500 dark:text-polar-400" />
              <span className="text-gray-700 dark:text-polar-200">
                {mockCommunityStats.members} Members
              </span>
            </div>
            <div className="flex flex-row items-center gap-2 text-sm">
              <FileText className="h-4 w-4 text-gray-500 dark:text-polar-400" />
              <span className="text-gray-700 dark:text-polar-200">
                {mockCommunityStats.posts} Posts
              </span>
            </div>
            <div className="flex flex-row items-center gap-2 text-sm">
              <Trophy className="h-4 w-4 text-gray-500 dark:text-polar-400" />
              <span className="text-gray-700 dark:text-polar-200">
                {mockCommunityStats.admins} Admin
              </span>
            </div>
          </div>

          {/* Member Avatars */}
          <div className="flex flex-col gap-2 pt-2 border-t border-gray-200 dark:border-polar-700">
            <h4 className="text-sm font-semibold dark:text-white">Members</h4>
            <div className="flex flex-row flex-wrap gap-2">
              {mockMembers.slice(0, 8).map((member, index) => (
                <Avatar
                  key={index}
                  name={member.name}
                  avatar_url={member.avatar_url}
                  className="h-8 w-8"
                />
              ))}
            </div>
          </div>

          {/* Invite Button */}
          <Button className="w-full mt-2" variant="default">
            INVITE MEMBERS
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

