'use client'

import { schemas } from '@polar-sh/client'
import Avatar from '@polar-sh/ui/components/atoms/Avatar'
import { Card, CardContent } from '@polar-sh/ui/components/atoms/Card'
import { Heart, MessageCircle, Pin, Calendar, Target, Hand } from 'lucide-react'
import Image from 'next/image'

interface PublicCommunityContentProps {
  organization: schemas['Organization']
}

const defaultPreviewPosts = [
  {
    id: '1',
    author: {
      name: '<PERSON>',
      avatar_url: null,
      badge: '3',
    },
    title: 'Bem-vindos novos membros!',
    content:
      'Fico muito feliz em ver nossa comunidade crescendo! Esta semana temos conteúdo incrível chegando, incluindo uma sessão de Q&A ao vivo na sexta-feira. Não esqueçam de conferir os recursos fixados na seção Start Here.',
    category: 'Anúncios',
    timeAgo: '2 horas atrás',
    likes: 24,
    comments: 8,
    isFeatured: true,
    icon: <Pin className="h-4 w-4" />,
    image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&q=80',
  },
  {
    id: '2',
    author: {
      name: 'Michael Chen',
      avatar_url: null,
      badge: '2',
    },
    title: 'Sessão de Coaching em Grupo',
    content:
      'A sessão de coaching em grupo desta semana está agendada para quinta-feira às 15h EST. Vamos cobrir estratégias avançadas e responder suas perguntas. Nos vemos lá!',
    category: 'Eventos',
    timeAgo: '5 horas atrás',
    likes: 18,
    comments: 5,
    isFeatured: false,
    icon: <Calendar className="h-4 w-4" />,
  },
  {
    id: '3',
    author: {
      name: 'Emma Rodriguez',
      avatar_url: null,
      badge: '1',
    },
    title: 'Novo Módulo de Aprendizado Disponível',
    content:
      'Acabei de lançar um novo módulo abrangente sobre técnicas avançadas. Confira na seção Learning. Adoraria ouvir seu feedback!',
    category: 'Aprendizado',
    timeAgo: '1 dia atrás',
    likes: 32,
    comments: 12,
    isFeatured: false,
    icon: null,
  },
]

const dietaDaSelvaPosts = [
  {
    id: '1',
    author: {
      name: 'Dr. Rafael Silva',
      avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&q=80',
      badge: '3',
    },
    title: '🌿 Bem-vindos à Dieta da Selva!',
    content:
      'Olá, selva! 🌳\n\nQue alegria ter você aqui! Esta comunidade foi criada para todos que buscam uma vida mais natural, saudável e conectada com a natureza.\n\nA Dieta da Selva não é apenas sobre comida - é sobre transformação. É sobre voltar ao essencial, ao que nosso corpo realmente precisa.\n\n📌 COMEÇE AQUI:\n1. Leia o guia "Start Here" (fixado)\n2. Apresente-se na seção Introductions\n3. Participe do desafio de 7 dias\n\nEstou aqui para ajudar vocês nessa jornada. Qualquer dúvida, é só chamar!\n\nVamos juntos transformar nossa relação com a comida! 💚',
    category: 'Anúncios',
    timeAgo: '2 dias atrás',
    likes: 89,
    comments: 23,
    isFeatured: true,
    icon: <Pin className="h-4 w-4" />,
    image: null,
  },
  {
    id: '2',
    author: {
      name: 'Dr. Rafael Silva',
      avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&q=80',
      badge: '3',
    },
    title: '🎯 Desafio 7 Dias Sem Açúcar Refinado',
    content:
      'Vamos fazer um desafio juntos? 🚀\n\nPor 7 dias, vamos eliminar completamente o açúcar refinado da nossa alimentação.\n\n✅ PODE:\n- Frutas naturais\n- Mel (com moderação)\n- Açúcar de coco\n- Stevia natural\n\n❌ NÃO PODE:\n- Açúcar branco\n- Refrigerantes\n- Doces industrializados\n- Sucos de caixinha\n\nQuem topa? Comenta "EU TOPO" e vamos começar amanhã!\n\nVou postar dicas diárias para ajudar vocês. Juntos somos mais fortes! 💪',
    category: 'Desafios',
    timeAgo: '3 horas atrás',
    likes: 234,
    comments: 89,
    isFeatured: true,
    icon: <Target className="h-4 w-4" />,
    image: 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=800&q=80',
  },
  {
    id: '3',
    author: {
      name: 'Dr. Rafael Silva',
      avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&q=80',
      badge: '3',
    },
    title: '🥗 Receita: Salada Amazônica Energética',
    content:
      'Essa receita é perfeita para quem quer começar! Super simples e cheia de nutrientes.\n\nINGREDIENTES:\n- Folhas verdes (alface, rúcula, espinafre)\n- Tomate cereja\n- Abacate\n- Castanha-do-pará\n- Azeite extra virgem\n- Limão\n- Sal rosa do Himalaia\n\nMODO DE PREPARO:\n1. Lave bem as folhas\n2. Corte o tomate ao meio\n3. Fatias de abacate\n4. Quebre as castanhas\n5. Tempere com azeite, limão e sal\n\nEssa salada te dá energia para o dia todo! Quem já fez, comenta aí como ficou! 👇',
    category: 'Receitas',
    timeAgo: '5 horas atrás',
    likes: 156,
    comments: 42,
    isFeatured: false,
    icon: null,
    image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=800&q=80',
  },
  {
    id: '4',
    author: {
      name: 'Maria Santos',
      avatar_url: null,
      badge: '1',
    },
    title: '💡 Dica: Como fazer compras na feira',
    content:
      'Gente, aprendi uma dica super valiosa com o Dr. Rafael e quero compartilhar!\n\nSEMPRE vá à feira com fome! 😄 Mas sério, quando você vai com fome, escolhe melhor os alimentos frescos e naturais.\n\nOutra dica: compre sempre da estação. Frutas e verduras da época são mais baratas, mais saborosas e mais nutritivas!\n\nHoje comprei:\n- Banana da terra\n- Abacaxi\n- Couve\n- Batata doce\n- Ovos caipira\n\nTudo fresquinho e direto do produtor! Quem mais ama feira? 🥬',
    category: 'Dicas',
    timeAgo: '1 dia atrás',
    likes: 67,
    comments: 18,
    isFeatured: false,
    icon: null,
    image: null,
  },
  {
    id: '5',
    author: {
      name: 'João Oliveira',
      avatar_url: null,
      badge: '1',
    },
    title: '👋 Olá, sou novo aqui!',
    content:
      'Oi pessoal! Acabei de entrar na comunidade e estou super animado!\n\nTenho 32 anos, sou de São Paulo, e estou buscando uma mudança de vida. Trabalho muito, sempre comendo porcaria, e minha energia está no chão.\n\nDescobri a Dieta da Selva através de um amigo e fiquei curioso. Quero aprender mais sobre alimentação natural e melhorar minha saúde.\n\nAlguém tem alguma dica para iniciantes? Qual foi a primeira mudança que vocês fizeram?\n\nObrigado por me receberem! 🙏',
    category: 'Introductions',
    timeAgo: '6 horas atrás',
    likes: 45,
    comments: 12,
    isFeatured: false,
    icon: <Hand className="h-4 w-4" />,
    image: null,
  },
]

const getPreviewPosts = (slug: string) => {
  if (slug === 'dieta-da-selva') {
    return dietaDaSelvaPosts
  }
  return defaultPreviewPosts
}

export const PublicCommunityContent = ({
  organization,
}: PublicCommunityContentProps) => {
  const previewPosts = getPreviewPosts(organization.slug)
  
  const getAboutText = () => {
    if (organization.slug === 'dieta-da-selva') {
      return `A Dieta da Selva é uma filosofia de vida que nos reconecta com nossos ancestrais através da alimentação. Baseada nos princípios de simplicidade, naturalidade e conexão com a terra, esta comunidade reúne pessoas que buscam:

🌿 Alimentação 100% natural e não processada
🍃 Frutas e vegetais em seu estado mais puro
🐟 Proteínas de qualidade (peixes, ovos, carnes magras)
🌰 Gorduras saudáveis e óleos naturais
💪 Mais energia, saúde e bem-estar
🔥 Emagrecimento sustentável e natural

Aqui você encontrará:
- Receitas práticas e deliciosas
- Guias de alimentos da Amazônia
- Planos de refeições semanais
- Dicas de compras e preparo
- Suporte de uma comunidade engajada
- Lives semanais com especialistas
- Desafios mensais de transformação

Junte-se a milhares de pessoas que já transformaram suas vidas através da alimentação natural!`
    }
    return `Bem-vindo à comunidade ${organization.name}! Este é um espaço privado onde membros se reúnem para compartilhar conhecimento, apoiar uns aos outros e crescer pessoal e profissionalmente.`
  }

  return (
    <div className="w-full bg-white dark:bg-polar-950 py-12 md:py-16">
      <div className="max-w-4xl mx-auto px-4 md:px-8">
        {/* About Section */}
        <div className="mb-12">
          <h2 className="text-2xl md:text-3xl font-bold dark:text-white mb-4">
            Sobre esta comunidade
          </h2>
          <div className="prose prose-lg dark:prose-invert max-w-none">
            <p className="text-gray-700 dark:text-polar-300 leading-relaxed text-lg whitespace-pre-line">
              {getAboutText()}
            </p>
          </div>
        </div>

        {/* Preview Posts */}
        <div className="mb-8">
          <h2 className="text-2xl md:text-3xl font-bold dark:text-white mb-6">
            Veja o que está acontecendo
          </h2>
          <div className="flex flex-col gap-4">
            {previewPosts.map((post) => (
              <Card
                key={post.id}
                className="bg-white dark:bg-polar-800 border border-gray-200 dark:border-polar-700 hover:shadow-lg transition-shadow"
              >
                <CardContent className="p-6">
                  <div className="flex flex-col gap-4">
                    {/* Header */}
                    <div className="flex flex-row items-start justify-between">
                      <div className="flex flex-row items-center gap-3">
                        <div className="relative">
                          <Avatar
                            name={post.author.name}
                            avatar_url={post.author.avatar_url}
                            className="h-10 w-10"
                          />
                          {post.author.badge && (
                            <div className="absolute -bottom-1 -right-1 h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center border-2 border-white dark:border-polar-800">
                              <span className="text-xs text-white font-semibold">
                                {post.author.badge}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="flex flex-col">
                          <div className="flex flex-row items-center gap-2 flex-wrap">
                            <span className="font-semibold dark:text-white">
                              {post.author.name}
                            </span>
                            {post.title && (
                              <>
                                {post.icon && (
                                  <span className="text-gray-500 dark:text-polar-400">
                                    {post.icon}
                                  </span>
                                )}
                                <span className="font-medium dark:text-white">
                                  {post.title}
                                </span>
                              </>
                            )}
                          </div>
                          <div className="flex flex-row items-center gap-2 text-sm text-gray-500 dark:text-polar-400">
                            <span>{post.timeAgo}</span>
                            <span>•</span>
                            <span>{post.category}</span>
                          </div>
                        </div>
                      </div>
                      {post.isFeatured && (
                        <div className="flex items-center gap-1 text-yellow-500">
                          <Pin className="h-4 w-4" />
                        </div>
                      )}
                    </div>

                    {/* Image */}
                    {post.image && (
                      <div className="relative w-full h-64 rounded-lg overflow-hidden">
                        <Image
                          src={post.image}
                          alt={post.title || 'Post image'}
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}

                    {/* Content */}
                    <p className="text-gray-700 dark:text-polar-200 leading-relaxed whitespace-pre-line">
                      {post.content}
                    </p>

                    {/* Actions */}
                    <div className="flex flex-row items-center gap-6 pt-2 border-t border-gray-200 dark:border-polar-700">
                      <div className="flex flex-row items-center gap-2 text-gray-600 dark:text-polar-400">
                        <Heart className="h-5 w-5" />
                        <span>{post.likes}</span>
                      </div>
                      <div className="flex flex-row items-center gap-2 text-gray-600 dark:text-polar-400">
                        <MessageCircle className="h-5 w-5" />
                        <span>{post.comments}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div className="text-center py-8">
          <p className="text-gray-600 dark:text-polar-400 text-lg">
            Junte-se à comunidade para ver todos os posts e participar das
            discussões
          </p>
        </div>
      </div>
    </div>
  )
}

