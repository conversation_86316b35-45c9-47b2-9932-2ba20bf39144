import Link from 'next/link'
import { PropsWithChildren } from 'react'
import { twMerge } from 'tailwind-merge'
import { PolarLogotype } from '../Layout/Public/PolarLogotype'
import { mockPublicCommunities } from '../Community/mockPublicCommunities'

const Footer = () => {
  return (
    <div className="mt-16 flex w-full flex-col items-center gap-y-12 bg-white dark:bg-black">
      <div
        className={twMerge(
          'flex w-full flex-col items-center px-6 py-16 md:max-w-3xl md:px-0 xl:max-w-6xl',
        )}
      >
        <div
          className={twMerge(
            'grid w-full grid-cols-1 gap-12 md:grid-cols-2 md:justify-between md:gap-16 lg:grid-cols-5',
          )}
        >
          <div className="flex flex-1 flex-col gap-y-6">
            <span className="text-black md:ml-0 dark:text-white">
              <PolarLogotype
                className="ml-2 md:ml-0"
                logoVariant="icon"
                size={40}
              />
            </span>
            <p className="dark:text-polar-400 max-w-xs text-sm leading-relaxed text-gray-600">
              A plataforma completa para creators brasileiros venderem produtos digitais, comunidades e assinaturas.
            </p>
            <span className="dark:text-polar-500 text-sm text-gray-500">
              &copy; Fluu Digital 
            </span>
          </div>

          <div className="flex flex-col gap-y-4">
            <h3 className="text-base font-semibold dark:text-white">Recursos</h3>
            <div className="flex flex-col gap-y-2">
              <FooterLink href="/features/products">
                Produtos Digitais
              </FooterLink>
              <FooterLink href="/features/finance">
                PIX e Pagamentos
              </FooterLink>
              <FooterLink href="/features/usage-billing">
                Assinaturas
              </FooterLink>
              <FooterLink href="/features/products">
                Comunidades
              </FooterLink>
              <FooterLink href="/features/benefits">
                Entregas Automáticas
              </FooterLink>
              <FooterLink href="/features/analytics">
                Analytics
              </FooterLink>
              <FooterLink href="/resources/pricing">
                Preços
              </FooterLink>
            </div>
          </div>

          <div className="flex flex-col gap-y-4">
            <h3 className="text-base font-semibold dark:text-white">Empresa</h3>
            <div className="flex flex-col gap-y-2">
              <FooterLink href="/company">Sobre a Fluu</FooterLink>
              <FooterLink href="/login">Começar Agora</FooterLink>
              <FooterLink href="/legal/terms">
                Termos de Serviço
              </FooterLink>
              <FooterLink href="/legal/privacy">
                Política de Privacidade
              </FooterLink>
            </div>
          </div>

          <div className="flex flex-col gap-y-4">
            <h3 className="text-base font-semibold dark:text-white">
              Comunidades
            </h3>
            <div className="flex flex-col gap-y-2">
              {mockPublicCommunities.slice(0, 5).map((community) => (
                <FooterLink
                  key={community.slug}
                  href={`/${community.slug}/community`}
                >
                  {community.name}
                </FooterLink>
              ))}
            </div>
          </div>

          <div className="flex flex-col gap-y-4">
            <h3 className="text-base font-semibold dark:text-white">Suporte</h3>
            <div className="flex flex-col gap-y-2">
              <FooterLink href="mailto:<EMAIL>">
                <EMAIL>
              </FooterLink>
              <FooterLink href="https://discord.gg/fluu" target="_blank">
                Discord
              </FooterLink>
              <FooterLink href="https://x.com/fluu_br" target="_blank">
                X / Twitter
              </FooterLink>
              <FooterLink href="/status">Status do Sistema</FooterLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Footer

const FooterLinkClassnames =
  'dark:text-polar-500 dark:hover:text-polar-50 flex flex-row items-center gap-x-1 text-gray-500 transition-colors hover:text-gray-500'

const FooterLink = (
  props: PropsWithChildren<{ href: string; target?: string }>,
) => {
  const isExternal = props.href.toString().startsWith('http')
  const { target, href, ...restProps } = props

  if (isExternal) {
    return (
      <a
        className={FooterLinkClassnames}
        href={href}
        target={target || '_blank'}
        rel={target === '_blank' ? 'noopener noreferrer' : undefined}
        {...restProps}
      >
        {props.children}
      </a>
    )
  }

  return (
    <Link className={FooterLinkClassnames} href={href} {...restProps}>
      {props.children}
    </Link>
  )
}
