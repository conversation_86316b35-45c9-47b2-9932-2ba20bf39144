{"$schema": "https://openapi.vercel.sh/vercel.json", "installCommand": "cd ../.. && pnpm install", "buildCommand": "cd ../.. && pnpm run build --filter=web", "outputDirectory": ".next", "framework": "nextjs", "git": {"deploymentEnabled": {"main": false}}, "rewrites": [{"source": "/_mintlify/api/:path+", "destination": "https://polar.mintlify.app/_mintlify/api/:path+"}, {"source": "/api/request", "destination": "https://polar.mintlify.app/_mintlify/api/request"}, {"source": "/docs", "destination": "https://polar.mintlify.dev/docs"}, {"source": "/docs/llms.txt", "destination": "https://polar.mintlify.app/llms.txt"}, {"source": "/docs/llms-full.txt", "destination": "https://polar.mintlify.app/llms-full.txt"}, {"source": "/docs/sitemap.xml", "destination": "https://polar.mintlify.app/sitemap.xml"}, {"source": "/docs/robots.txt", "destination": "https://polar.mintlify.app/robots.txt"}, {"source": "/docs/:path*", "destination": "https://polar.mintlify.dev/docs/:path*"}, {"source": "/mintlify-assets/:path+", "destination": "https://polar.mintlify.app/mintlify-assets/:path+"}, {"source": "/ingest/static/(.*)", "destination": "https://us-assets.i.posthog.com/static/$1"}, {"source": "/ingest/(.*)", "destination": "https://us.i.posthog.com/$1"}], "trailingSlash": false}