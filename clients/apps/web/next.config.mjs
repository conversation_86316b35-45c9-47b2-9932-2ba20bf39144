/* global process */
import bundleAnalyzer from '@next/bundle-analyzer'
import createMDX from '@next/mdx'
import { withSentryConfig } from '@sentry/nextjs'
import { themeConfig } from './shiki.config.mjs'

const POLAR_AUTH_COOKIE_KEY =
  process.env.POLAR_AUTH_COOKIE_KEY || 'fluu_session'
const ENVIRONMENT =
  process.env.VERCEL_ENV || process.env.NEXT_PUBLIC_VERCEL_ENV || 'development'
const CODESPACES = process.env.CODESPACES === 'true'

const defaultFrontendHostname = process.env.NEXT_PUBLIC_FRONTEND_BASE_URL
  ? new URL(process.env.NEXT_PUBLIC_FRONTEND_BASE_URL).hostname
  : 'fluu.digital'

const S3_PUBLIC_IMAGES_BUCKET_ORIGIN = process.env
  .S3_PUBLIC_IMAGES_BUCKET_HOSTNAME
  ? `${process.env.S3_PUBLIC_IMAGES_BUCKET_PROTOCOL || 'https'}://${process.env.S3_PUBLIC_IMAGES_BUCKET_HOSTNAME}${process.env.S3_PUBLIC_IMAGES_BUCKET_PORT ? `:${process.env.S3_PUBLIC_IMAGES_BUCKET_PORT}` : ''}`
  : ''
// Ensure API URL is available for CSP with fallback
// Use same fallback as config.ts: http://127.0.0.1:8000 in development, https://api.fluu.digital in production
const apiUrlForCSP = process.env.NEXT_PUBLIC_API_URL || 
  (ENVIRONMENT === 'development' ? 'http://127.0.0.1:8000' : 'https://api.fluu.digital')
// Extrair domínio da API para CSP (sem protocolo, apenas domínio)
const apiDomain = apiUrlForCSP.replace(/^https?:\/\//, '').split('/')[0]
const baseCSP = `
    default-src 'self';
    connect-src 'self' ${apiUrlForCSP} ${process.env.S3_UPLOAD_ORIGINS} https://api.stripe.com https://maps.googleapis.com https://*.google-analytics.com https://chat.uk.plain.com https://prod-uk-services-attachm-attachmentsuploadbucket2-1l2e4906o2asm.s3.eu-west-2.amazonaws.com;
    frame-src 'self' https://*.js.stripe.com https://js.stripe.com https://hooks.stripe.com;
    script-src 'self' 'unsafe-eval' 'unsafe-inline' https://*.js.stripe.com https://js.stripe.com https://maps.googleapis.com https://www.googletagmanager.com https://chat.cdn-plain.com;
    style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
    img-src 'self' blob: data: https://www.gravatar.com https://lh3.googleusercontent.com https://avatars.githubusercontent.com https://images.unsplash.com ${S3_PUBLIC_IMAGES_BUCKET_ORIGIN} https://prod-uk-services-workspac-workspacefilespublicbuck-vs4gjqpqjkh6.s3.amazonaws.com https://prod-uk-services-attachm-attachmentsbucket28b3ccf-uwfssb4vt2us.s3.eu-west-2.amazonaws.com https://i0.wp.com;
    font-src 'self';
    object-src 'none';
    base-uri 'self';
    ${ENVIRONMENT !== 'development' ? 'upgrade-insecure-requests;' : ''}
`
// Ensure API URL is properly formatted for CSP
// Use the same API URL variable for consistency
const apiUrl = apiUrlForCSP
// Para form-action, usar o domínio completo
// Em desenvolvimento, sempre permitir HTTP para localhost/127.0.0.1
// Em produção, usar apenas HTTPS com o domínio da API
const isLocalhost = apiDomain.includes('127.0.0.1') || apiDomain.includes('localhost')
const getProtocol = (url) => url.startsWith('https://') ? 'https' : 'http'
const apiProtocol = getProtocol(apiUrlForCSP)

// Construir form-action directive
// Em desenvolvimento com localhost, usar apenas as URLs locais (evitar duplicação)
// Em desenvolvimento sem localhost, incluir a URL da API + URLs locais
// Em produção, usar apenas HTTPS com o domínio da API
const formActionDirective = ENVIRONMENT === 'development'
  ? isLocalhost
    ? `form-action 'self' http://127.0.0.1:8000 http://localhost:8000;`
    : `form-action 'self' ${apiProtocol}://${apiDomain} http://127.0.0.1:8000 http://localhost:8000;`
  : `form-action 'self' https://${apiDomain};`
// Normalize CSP: remove extra spaces and ensure proper formatting
const normalizeCSP = (csp) => csp.replace(/\s+/g, ' ').trim()
const nonEmbeddedCSP = normalizeCSP(`${baseCSP.trim()} ${formActionDirective.trim()} frame-ancestors 'none';`)
const embeddedCSP = normalizeCSP(`${baseCSP.trim()} ${formActionDirective.trim()} frame-ancestors *;`)
// Don't add form-action to the OAuth2 authorize page, as it blocks the OAuth2 redirection
// 10-years old debate about whether to block redirects with form-action or not: https://github.com/w3c/webappsec-csp/issues/8
const oauth2CSP = `${baseCSP.trim()} frame-ancestors 'none';`

// We rewrite Mintlify docs to fluu.digital/docs, so we need a specific CSP for them
// Ref: https://www.mintlify.com/docs/guides/csp-configuration#content-security-policy-csp-configuration
const docsCSP = `
  default-src 'self';
  script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com;
  style-src 'self' 'unsafe-inline' d4tuoctqmanu0.cloudfront.net;
  font-src 'self' d4tuoctqmanu0.cloudfront.net cdn.jsdelivr.net fonts.cdnfonts.com;
  img-src 'self' data: blob: d3gk2c5xim1je2.cloudfront.net mintcdn.com mintlify.s3.us-west-1.amazonaws.com;
  connect-src 'self' *.mintlify.dev *.mintlify.com https://*.google-analytics.com;
  frame-src 'self' *.mintlify.dev *.mintlify.com;
`

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  transpilePackages: ['shiki'],
  pageExtensions: ['js', 'jsx', 'md', 'mdx', 'ts', 'tsx'],

  // This is required to support PostHog trailing slash API requests
  skipTrailingSlashRedirect: true,

  webpack: (config, { dev }) => {
    if (config.cache && !dev) {
      config.cache = Object.freeze({
        type: 'memory',
      })
    }

    return config
  },

  // Since Codespaces run behind a proxy, we need to allow it for Server-Side Actions, like cache revalidation
  // See: https://github.com/vercel/next.js/issues/58019
  ...(CODESPACES
    ? {
        experimental: {
          serverActions: {
            allowedForwardedHosts: [
              `${process.env.CODESPACE_NAME}-8080.${process.env.GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN}`,
              'localhost:8080',
              '127.0.0.1:8080',
            ],
            allowedOrigins: [
              `${process.env.CODESPACE_NAME}-8080.${process.env.GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN}`,
              'localhost:8080',
              '127.0.0.1:8080',
            ],
          },
        },
      }
    : {}),

  images: {
    remotePatterns: [
      ...(process.env.S3_PUBLIC_IMAGES_BUCKET_HOSTNAME
        ? [
            {
              protocol: process.env.S3_PUBLIC_IMAGES_BUCKET_PROTOCOL || 'https',
              hostname: process.env.S3_PUBLIC_IMAGES_BUCKET_HOSTNAME,
              port: process.env.S3_PUBLIC_IMAGES_BUCKET_PORT || '',
              pathname: process.env.S3_PUBLIC_IMAGES_BUCKET_PATHNAME || '**',
            },
          ]
        : []),
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: '7vk6rcnylug0u6hg.public.blob.vercel-storage.com',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '**',
      },
    ],
  },

  async redirects() {
    return [
      // dashboard.fluu.digital redirections
      {
        source: '/',
        destination: '/login',
        has: [
          {
            type: 'host',
            value: 'dashboard.fluu.digital',
          },
        ],
        permanent: false,
      },
      ...(ENVIRONMENT === 'production'
        ? [
            {
              source: '/:client_secret(fluu_cl_.*)',
              destination:
                'https://api.fluu.digital/v1/checkout-links/:client_secret/redirect',
              has: [
                {
                  type: 'host',
                  value: 'buy.fluu.digital',
                },
              ],
              permanent: false,
            },
            {
              source: '/:id',
              destination: 'https://fluu.digital/api/checkout?price=:id*',
              has: [
                {
                  type: 'host',
                  value: 'buy.fluu.digital',
                },
              ],
              permanent: false,
            },
          ]
        : []),
      {
        source: '/:path*',
        destination: 'https://fluu.digital/:path*',
        has: [
          {
            type: 'host',
            value: 'dashboard.fluu.digital',
          },
        ],
        permanent: false,
      },
      {
        source: '/careers',
        destination: 'https://fluu.digital/company',
        permanent: false,
      },
      {
        source: '/llms.txt',
        destination: 'https://fluu.digital/docs/llms.txt',
        permanent: true,
        has: [
          {
            type: 'host',
            value: 'fluu.digital',
          },
        ],
      },
      {
        source: '/llms-full.txt',
        destination: 'https://fluu.digital/docs/llms-full.txt',
        permanent: true,
        has: [
          {
            type: 'host',
            value: 'fluu.digital',
          },
        ],
      },

      // Logged-in user redirections
      {
        source: '/',
        destination: '/start',
        has: [
          {
            type: 'cookie',
            key: POLAR_AUTH_COOKIE_KEY,
          },
          {
            type: 'host',
            value: defaultFrontendHostname,
          },
        ],
        permanent: false,
      },

      // Redirect /maintainer to fluu.digital if on a different domain name
      {
        source: '/dashboard/:path*',
        destination: `https://${defaultFrontendHostname}/dashboard/:path*`,
        missing: [
          {
            type: 'host',
            value: defaultFrontendHostname,
          },
          {
            type: 'header',
            key: 'x-forwarded-host',
            value: defaultFrontendHostname,
          },
        ],
        permanent: false,
      },

      {
        source: '/maintainer',
        destination: '/dashboard',
        permanent: true,
      },
      {
        source: '/maintainer/:path(.*)',
        destination: '/dashboard/:path(.*)',
        permanent: true,
      },
      {
        source: '/finance',
        destination: '/finance/income',
        permanent: false,
      },
      {
        source: '/dashboard/:organization/overview',
        destination: '/dashboard/:organization',
        permanent: true,
      },
      {
        source: '/dashboard/:organization/products/benefits',
        destination: '/dashboard/:organization/benefits',
        permanent: true,
      },
      {
        source: '/dashboard/:organization/products/overview',
        destination: '/dashboard/:organization/products',
        permanent: true,
      },
      {
        source: '/dashboard/:organization/issues',
        destination: '/dashboard/:organization/issues/overview',
        permanent: false,
      },
      {
        source: '/dashboard/:organization/promote/issues',
        destination: '/dashboard/:organization/issues/badge',
        permanent: false,
      },
      {
        source: '/dashboard/:organization/issues/promote',
        destination: '/dashboard/:organization/issues/badge',
        permanent: false,
      },
      {
        source: '/dashboard/:organization/finance',
        destination: '/dashboard/:organization/finance/income',
        permanent: false,
      },

      // Account Settings Redirects
      {
        source: '/settings',
        destination: '/dashboard/account/preferences',
        permanent: true,
      },

      // Access tokens redirect
      {
        source: '/settings/tokens',
        destination: '/account/developer',
        permanent: false,
      },

      // Old blog redirects
      {
        source: '/polarsource/posts',
        destination: '/blog',
        permanent: false,
      },
      {
        source: '/polarsource/posts/:path(.*)',
        destination: '/blog/:path*',
        permanent: false,
      },

      // Fallback blog redirect
      {
        source: '/:path*',
        destination: 'https://fluu.digital/blog',
        has: [
          {
            type: 'host',
            value: 'blog.fluu.digital',
          },
        ],
        permanent: false,
      },
    ]
  },
  async headers() {
    const baseHeaders = [
      {
        key: 'Content-Security-Policy',
        value: nonEmbeddedCSP,
      },
      {
        key: 'Permissions-Policy',
        value:
          'payment=(), publickey-credentials-get=(), camera=(), microphone=(), geolocation=()',
      },
      {
        key: 'X-Frame-Options',
        value: 'DENY',
      },
    ]

    // Add X-Robots-Tag header for sandbox environment
    if (ENVIRONMENT === 'sandbox') {
      baseHeaders.push({
        key: 'X-Robots-Tag',
        value: 'noindex, nofollow, noarchive, nosnippet, noimageindex',
      })
    }

    return [
      {
        source: '/((?!checkout|oauth2|docs).*)',
        headers: baseHeaders,
      },
      {
        source: '/oauth2/:path*',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: normalizeCSP(oauth2CSP),
          },
          {
            key: 'Permissions-Policy',
            value:
              'payment=(), publickey-credentials-get=(), camera=(), microphone=(), geolocation=()',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          ...(ENVIRONMENT === 'sandbox'
            ? [
                {
                  key: 'X-Robots-Tag',
                  value:
                    'noindex, nofollow, noarchive, nosnippet, noimageindex',
                },
              ]
            : []),
        ],
      },
      {
        source: '/checkout/:path*',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: embeddedCSP,
          },
          {
            key: 'Permissions-Policy',
            value: `payment=*, publickey-credentials-get=*, camera=(), microphone=(), geolocation=()`,
          },
          ...(ENVIRONMENT === 'sandbox'
            ? [
                {
                  key: 'X-Robots-Tag',
                  value:
                    'noindex, nofollow, noarchive, nosnippet, noimageindex',
                },
              ]
            : []),
        ],
      },
      {
        source: '/docs/:path*',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: normalizeCSP(docsCSP.trim()),
          },
          {
            key: 'Permissions-Policy',
            value:
              'payment=(), publickey-credentials-get=(), camera=(), microphone=(), geolocation=()',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          ...(ENVIRONMENT === 'sandbox'
            ? [
                {
                  key: 'X-Robots-Tag',
                  value:
                    'noindex, nofollow, noarchive, nosnippet, noimageindex',
                },
              ]
            : []),
        ],
      },
    ]
  },
}

const createConfig = async () => {
  const withMDX = createMDX({
    options: {
      remarkPlugins: ['remark-frontmatter', 'remark-gfm'],
      rehypePlugins: [
        'rehype-mdx-import-media',
        'rehype-slug',
        [
          '@shikijs/rehype',
          {
            themes: themeConfig,
          },
        ],
      ],
    },
  })

  let conf = withMDX(nextConfig)

  // Injected content via Sentry wizard below

  conf = withSentryConfig(conf, {
    // For all available options, see:
    // https://github.com/getsentry/sentry-webpack-plugin#options

    org: 'fluu',
    project: 'dashboard',

    // Pass the auth token
    authToken: process.env.SENTRY_AUTH_TOKEN,

    // Only print logs for uploading source maps in CI
    silent: !process.env.CI,

    // For all available options, see:
    // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

    // Upload a larger set of source maps for prettier stack traces (increases build time)
    widenClientFileUpload: true,

    reactComponentAnnotation: {
      enabled: false,
    },

    // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
    // This can increase your server load as well as your hosting bill.
    // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
    // side errors will fail.
    tunnelRoute: '/monitoring',

    // Hides source maps from generated client bundles
    hideSourceMaps: true,

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,

    // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
    // See the following for more information:
    // https://docs.sentry.io/product/crons/
    // https://vercel.com/docs/cron-jobs
    automaticVercelMonitors: true,
  })

  if (process.env.ANALYZE === 'true') {
    const withBundleAnalyzer = bundleAnalyzer({
      enabled: true,
    })
    conf = withBundleAnalyzer(conf)
  }

  return conf
}

export default createConfig
