# Code Review Prompt - Frontend TypeScript Errors

## Contexto do Projeto
Este é um projeto SSO (Single Sign-On) com 3 componentes principais, focado em integração entre sistemas usando Better Auth, React e TypeScript.

## Objetivo
Realizar code review e corrigir os erros de TypeScript restantes no frontend, garantindo que o código compile sem erros.

## Erros Atuais (TypeScript)

### 1. Import Badge Component (PixStatus.tsx)
**Arquivo:** `/Users/<USER>/Documents/www/Gateways/polar/clients/apps/web/src/components/Dashboard/Orders/PixStatus.tsx`
**Erro:** `Cannot find module 'polarkit/components/ui/badge'`
**Linha:** 2

**Tarefa:** Encontrar o caminho correto de import para o componente Badge no projeto. Verificar outros arquivos que usam Badge para identificar o import correto.

### 2. Verificar Componentes Criados
**Arquivos criados que precisam revisão:**
- `/Users/<USER>/Documents/www/Gateways/polar/clients/apps/web/src/components/FluuCheckout.tsx` - Novo componente de checkout otimizado
- `/Users/<USER>/Documents/www/Gateways/polar/clients/apps/web/src/components/CustomerPortal/FluuPortalOverview.tsx` - Novo componente de portal
- `/Users/<USER>/Documents/www/Gateways/polar/clients/apps/web/src/components/Dashboard/Products/Benefits/TelegramBenefitForm.tsx` - Formulário Telegram
- `/Users/<USER>/Documents/www/Gateways/polar/clients/apps/web/src/components/Dashboard/Products/Benefits/WhatsAppBenefitForm.tsx` - Formulário WhatsApp
- `/Users/<USER>/Documents/www/Gateways/polar/clients/apps/web/src/components/Dashboard/Orders/PixStatus.tsx` - Status PIX

## Modificações Recentes
- ✅ Corrigido tipos em `Benefit/utils.tsx` para aceitar 'telegram' e 'whatsapp'
- ✅ Corrigido tipos em `BenefitForm.tsx` usando `any` temporariamente
- ✅ Adicionado tipo extendido para `payment_processor_metadata` em `PixStatus.tsx`
- ✅ Corrigido imports relativos para Telegram/WhatsApp forms

## Próximos Passos
1. **Corrigir import do Badge:** Encontrar o caminho correto do componente Badge no projeto
2. **Validar tipos:** Garantir que todos os componentes estejam usando tipos consistentes
3. **Testar compilação:** Rodar `npm run typecheck` até não haver mais erros
4. **Verificar runtime:** Garantir que os componentes funcionem corretamente após as correções

## Comandos Úteis
```bash
cd /Users/<USER>/Documents/www/Gateways/polar/clients/apps/web
npm run typecheck
```

## Considerações Importantes
- O projeto usa Tailwind CSS e componentes customizados
- Os formulários Telegram/WhatsApp usam `any` temporariamente até os schemas serem adicionados no backend
- O componente PixStatus espera `payment_processor_metadata` que deve vir do backend
- Priorize correções que não quebrem a funcionalidade existente

## Critérios de Sucesso
- [ ] Zero erros de TypeScript ao rodar `npm run typecheck`
- [ ] Todos os imports estão corretos e funcionando
- [ ] Componentes mantêm funcionalidade após correções
- [ ] Código segue padrões existentes do projeto