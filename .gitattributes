# Git Attributes para Proteção de Customizações
# Este arquivo ajuda a proteger customizações durante merges com upstream

# ============================================
# ARQUIVOS TOTALMENTE CUSTOMIZADOS
# Nunca fazer merge automático - sempre manter nossa versão
# ============================================

# Frontend totalmente customizado
clients/** merge=ours
clients/** -diff

# Documentação local
_docs/** merge=ours

# Providers de pagamento customizados (nunca sobrescrever)
server/polar/integrations/payment_providers/pagarme/** merge=ours
server/polar/integrations/payment_providers/mercado_pago/** merge=ours
server/polar/integrations/payment_providers/pix/** merge=ours

# Scripts de deploy customizados
deploy/** merge=ours

# ============================================
# ARQUIVOS MODIFICADOS (PRECISA REVISÃO)
# Merge union - combina ambas as versões
# ============================================

# Enum de PaymentProcessor (adicionamos pagarme, upstream pode adicionar outros)
server/polar/enums.py merge=union

# Registry de providers (registramos novos providers)
server/polar/integrations/payment_providers/__init__.py merge=union

# Serviços que modificamos para suportar novos providers
# (precisa revisão manual sempre)
server/polar/order/service.py merge=union
server/polar/checkout/service.py merge=union
server/polar/payment/service.py merge=union
server/polar/payment_method/service.py merge=union

# ============================================
# ARQUIVOS DE CONFIGURAÇÃO
# ============================================

# Configurações locais
.env* merge=ours
*.local merge=ours

# Lock files (manter nossa versão)
pnpm-lock.yaml merge=ours
uv.lock merge=ours


