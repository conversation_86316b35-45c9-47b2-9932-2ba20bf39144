from urllib.parse import urlencode

from fastapi import FastAP<PERSON>, Request
from fastapi.encoders import jsonable_encoder
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse, RedirectResponse, Response

from polar.config import settings
from polar.exceptions import (
    PolarError,
    PolarRedirectionError,
    PolarRequestValidationError,
    ResourceNotModified,
)


async def polar_exception_handler(request: Request, exc: PolarError) -> JSONResponse:
    # Preserve CORS headers from request
    headers = dict(exc.headers) if exc.headers else {}
    
    # Add CORS headers if origin is present
    origin = request.headers.get("origin")
    if origin:
        from polar.config import settings, Environment
        
        # Check if origin should be allowed (simplified check for error responses)
        if settings.ENV == Environment.development:
            if "localhost" in origin or "127.0.0.1" in origin:
                headers["Access-Control-Allow-Origin"] = origin
                headers["Access-Control-Allow-Credentials"] = "true"
                headers["Access-Control-Allow-Methods"] = "*"
                headers["Access-Control-Allow-Headers"] = "*"
        elif settings.FRONTEND_BASE_URL and origin in [settings.FRONTEND_BASE_URL]:
            headers["Access-Control-Allow-Origin"] = origin
            headers["Access-Control-Allow-Credentials"] = "true"
    
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": type(exc).__name__, "detail": exc.message},
        headers=headers,
    )


async def request_validation_exception_handler(
    request: Request, exc: RequestValidationError | PolarRequestValidationError
) -> JSONResponse:
    # Preserve CORS headers
    headers = {}
    origin = request.headers.get("origin")
    if origin:
        from polar.config import settings, Environment
        if settings.ENV == Environment.development:
            if "localhost" in origin or "127.0.0.1" in origin:
                headers["Access-Control-Allow-Origin"] = origin
                headers["Access-Control-Allow-Credentials"] = "true"
                headers["Access-Control-Allow-Methods"] = "*"
                headers["Access-Control-Allow-Headers"] = "*"
        elif settings.FRONTEND_BASE_URL and origin in [settings.FRONTEND_BASE_URL]:
            headers["Access-Control-Allow-Origin"] = origin
            headers["Access-Control-Allow-Credentials"] = "true"
    return JSONResponse(
        status_code=422,
        content={"error": type(exc).__name__, "detail": jsonable_encoder(exc.errors())},
        headers=headers,
    )


async def polar_redirection_exception_handler(
    request: Request, exc: PolarRedirectionError
) -> RedirectResponse:
    error_url_params = urlencode(
        {
            "message": exc.message,
            "return_to": exc.return_to or settings.FRONTEND_DEFAULT_RETURN_PATH,
        }
    )
    error_url = f"{settings.generate_frontend_url('/error')}?{error_url_params}"
    return RedirectResponse(error_url, 303)


async def polar_not_modified_handler(
    request: Request, exc: ResourceNotModified
) -> Response:
    return Response(status_code=exc.status_code)


async def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle unhandled exceptions and preserve CORS headers."""
    import structlog
    from polar.logging import Logger

    log: Logger = structlog.get_logger()
    log.error("Unhandled exception", error=str(exc), exc_info=True)
    
    # Preserve CORS headers
    headers = {}
    origin = request.headers.get("origin")
    if origin:
        from polar.config import settings, Environment
        if settings.ENV == Environment.development:
            if "localhost" in origin or "127.0.0.1" in origin:
                headers["Access-Control-Allow-Origin"] = origin
                headers["Access-Control-Allow-Credentials"] = "true"
                headers["Access-Control-Allow-Methods"] = "*"
                headers["Access-Control-Allow-Headers"] = "*"
        elif settings.FRONTEND_BASE_URL and origin in [settings.FRONTEND_BASE_URL]:
            headers["Access-Control-Allow-Origin"] = origin
            headers["Access-Control-Allow-Credentials"] = "true"
    
    return JSONResponse(
        status_code=500,
        content={"error": "InternalServerError", "detail": "An internal server error occurred."},
        headers=headers,
    )


def add_exception_handlers(app: FastAPI) -> None:
    app.add_exception_handler(
        PolarRedirectionError,
        polar_redirection_exception_handler,  # type: ignore
    )
    app.add_exception_handler(
        ResourceNotModified,
        polar_not_modified_handler,  # type: ignore
    )

    app.add_exception_handler(
        RequestValidationError,
        request_validation_exception_handler,  # type: ignore
    )
    app.add_exception_handler(
        PolarRequestValidationError,
        request_validation_exception_handler,  # type: ignore
    )
    app.add_exception_handler(PolarError, polar_exception_handler)  # type: ignore
    # Add generic exception handler last to catch all unhandled exceptions
    app.add_exception_handler(Exception, generic_exception_handler)  # type: ignore
