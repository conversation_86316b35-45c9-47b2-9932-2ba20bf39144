"""Schemas específicos do Pagar.me."""

from typing import Any, Optional
from pydantic import BaseModel


class PagarmeWebhookEvent(BaseModel):
    """Schema para eventos de webhook do Pagar.me."""

    type: str
    id: str
    created_at: Optional[str] = None
    data: dict[str, Any]


class PagarmeChargeData(BaseModel):
    """Schema para dados de charge do Pagar.me."""

    id: str
    status: str
    amount: int
    currency: str
    payment_method: str
    customer: Optional[dict[str, Any]] = None
    last_transaction: Optional[dict[str, Any]] = None
    metadata: Optional[dict[str, str]] = None


class PagarmeOrderData(BaseModel):
    """Schema para dados de order do Pagar.me."""

    id: str
    status: str
    amount: int
    currency: str
    customer_id: str
    charges: Optional[list[dict[str, Any]]] = None
    metadata: Optional[dict[str, str]] = None

