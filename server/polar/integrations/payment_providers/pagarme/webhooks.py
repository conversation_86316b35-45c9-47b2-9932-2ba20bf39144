"""Endpoints de webhook do Pagar.me."""

import structlog
from fastapi import Depends, HTTPException, Request
from typing import Any

from polar.config import settings
from polar.enums import PaymentProcessor
from polar.external_event.service import external_event as external_event_service
from polar.models.external_event import ExternalEventSource
from polar.postgres import AsyncSession, get_db_session
from polar.routing import APIRouter
from polar.integrations.payment_providers.registry import PaymentProviderRegistry
from polar.integrations.payment_providers.pagarme.schemas import PagarmeWebhookEvent

log = structlog.get_logger()

router = APIRouter(
    prefix="/integrations/pagarme",
    tags=["integrations_pagarme"],
    include_in_schema=False,
)

# Eventos principais do Pagar.me que processamos
IMPLEMENTED_WEBHOOKS = {
    "charge.paid",
    "charge.pending",
    "charge.failed",
    "charge.canceled",
    "charge.refunded",
    "order.paid",
    "order.pending",
    "order.payment_failed",
}


async def enqueue(
    session: AsyncSession,
    event_type: str,
    event_id: str,
    event_data: dict[str, Any],
) -> None:
    """Enfileira evento do Pagar.me para processamento."""
    task_name = f"pagarme.webhook.{event_type}"
    await external_event_service.enqueue(
        session,
        ExternalEventSource.pagarme,  # Precisará ser adicionado ao enum
        task_name,
        event_id,
        event_data,
    )


@router.post("/webhook", status_code=202, name="integrations.pagarme.webhook")
async def webhook(
    request: Request,
    session: AsyncSession = Depends(get_db_session),
) -> dict[str, str]:
    """
    Endpoint para receber webhooks do Pagar.me.

    Pagar.me envia webhooks no formato:
    {
        "type": "charge.paid",
        "id": "evt_...",
        "created_at": "2025-01-27T10:00:00Z",
        "data": {
            "id": "ch_...",
            "status": "paid",
            ...
        }
    }
    """
    try:
        # Ler payload do request
        payload = await request.json()

        # Validar estrutura básica
        if "type" not in payload or "id" not in payload:
            log.warning("Invalid Pagar.me webhook payload", payload=payload)
            raise HTTPException(status_code=400, detail="Invalid webhook payload")

        event_type = payload["type"]
        event_id = payload.get("id", "unknown")
        event_data = payload

        # Validar assinatura do webhook (se configurado)
        if settings.PAGARME_WEBHOOK_SECRET:
            # TODO: Implementar validação de assinatura do Pagar.me
            # Pagar.me pode usar X-Hub-Signature-256 ou similar
            # Por enquanto, apenas logamos que deveria validar
            log.info(
                "Pagar.me webhook signature validation not implemented yet",
                event_id=event_id,
            )

        log.info(
            "Pagar.me webhook received",
            event_type=event_type,
            event_id=event_id,
        )

        # Processar apenas eventos implementados
        if event_type in IMPLEMENTED_WEBHOOKS:
            # Obter provider do registry
            try:
                provider = PaymentProviderRegistry.get(PaymentProcessor.pagarme)
            except ValueError:
                log.error(
                    "Pagar.me provider not registered",
                    event_type=event_type,
                    event_id=event_id,
                )
                raise HTTPException(
                    status_code=500, detail="Pagar.me provider not configured"
                )

            # Processar webhook no provider
            await provider.handle_webhook(event_type, event_data)

            # Enfileirar para processamento assíncrono
            await enqueue(session, event_type, event_id, event_data)

            return {"status": "ok", "event_id": event_id}
        else:
            log.info(
                "Pagar.me webhook event not implemented",
                event_type=event_type,
                event_id=event_id,
            )
            return {"status": "ignored", "event_id": event_id}

    except HTTPException:
        raise
    except Exception as e:
        log.error(
            "Error processing Pagar.me webhook",
            error=str(e),
            error_type=type(e).__name__,
        )
        raise HTTPException(status_code=500, detail="Error processing webhook") from e

