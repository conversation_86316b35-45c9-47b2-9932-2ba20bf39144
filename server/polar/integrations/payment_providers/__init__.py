"""Payment providers integration module."""

import structlog

from polar.config import settings
from polar.enums import PaymentProcessor
from polar.integrations.payment_providers.base import (
    PaymentProvider,
    PaymentProviderError,
)
from polar.integrations.payment_providers.registry import PaymentProviderRegistry

log = structlog.get_logger()

__all__ = [
    "PaymentProvider",
    "PaymentProviderError",
    "PaymentProviderRegistry",
]

# Registrar providers na inicialização
if settings.ENABLE_PAGARME:
    try:
        from polar.integrations.payment_providers.pagarme.provider import (
            PagarmeProvider,
        )

        pagarme_provider = PagarmeProvider()
        PaymentProviderRegistry.register(PaymentProcessor.pagarme, pagarme_provider)
        log.info("Pagar.me payment provider registered")
    except Exception as e:
        log.error(
            "Failed to register Pagar.me provider",
            error=str(e),
            error_type=type(e).__name__,
        )

