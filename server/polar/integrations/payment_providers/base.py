"""Base interface for payment providers."""

from abc import ABC, abstractmethod
from typing import Any, Optional

from polar.enums import PaymentProcessor
from polar.models.payment import PaymentStatus


class PaymentProviderError(Exception):
    """Base exception for payment provider errors."""

    pass


class PaymentProvider(ABC):
    """Interface base para todos os providers de pagamento."""

    @property
    @abstractmethod
    def processor(self) -> PaymentProcessor:
        """Retorna o PaymentProcessor deste provider."""
        pass

    @abstractmethod
    async def create_customer(
        self,
        email: str,
        name: Optional[str] = None,
        metadata: Optional[dict[str, str]] = None,
    ) -> str:
        """
        Cria um customer no provider.

        Args:
            email: Email do customer
            name: Nome do customer (opcional)
            metadata: Metadados adicionais (opcional)

        Returns:
            customer_id do provider
        """
        pass

    @abstractmethod
    async def create_payment_intent(
        self,
        amount: int,
        currency: str,
        customer_id: str,
        payment_method_id: Optional[str] = None,
        metadata: Optional[dict[str, str]] = None,
        description: Optional[str] = None,
        confirm: bool = False,
        off_session: bool = False,
    ) -> dict[str, Any]:
        """
        Cria um payment intent.

        Args:
            amount: Valor em centavos
            currency: Código da moeda (ex: 'usd', 'brl')
            customer_id: ID do customer no provider
            payment_method_id: ID do payment method (opcional)
            metadata: Metadados adicionais (opcional)
            description: Descrição do pagamento (opcional)
            confirm: Se deve confirmar o pagamento imediatamente
            off_session: Se o pagamento é off-session

        Returns:
            Dados do payment intent (provider-agnostic):
            {
                'id': str,
                'client_secret': Optional[str],
                'status': str,
                'amount': int,
                'currency': str,
            }
        """
        pass

    @abstractmethod
    async def create_setup_intent(
        self,
        customer_id: str,
        metadata: Optional[dict[str, str]] = None,
    ) -> dict[str, Any]:
        """
        Cria um setup intent para salvar payment method.

        Args:
            customer_id: ID do customer no provider
            metadata: Metadados adicionais (opcional)

        Returns:
            Dados do setup intent:
            {
                'id': str,
                'client_secret': Optional[str],
                'status': str,
            }
        """
        pass

    @abstractmethod
    async def create_customer_session(
        self,
        customer_id: str,
    ) -> dict[str, Any]:
        """
        Cria uma sessão de customer para checkout.

        Args:
            customer_id: ID do customer no provider

        Returns:
            Dados da sessão do customer
        """
        pass

    @abstractmethod
    async def retrieve_payment(
        self,
        payment_id: str,
    ) -> dict[str, Any]:
        """
        Recupera informações de um pagamento.

        Args:
            payment_id: ID do pagamento no provider

        Returns:
            Dados do pagamento
        """
        pass

    @abstractmethod
    async def create_refund(
        self,
        payment_id: str,
        amount: Optional[int] = None,
        reason: Optional[str] = None,
    ) -> dict[str, Any]:
        """
        Cria um reembolso.

        Args:
            payment_id: ID do pagamento no provider
            amount: Valor do reembolso em centavos (opcional, se None reembolsa total)
            reason: Motivo do reembolso (opcional)

        Returns:
            Dados do reembolso
        """
        pass

    @abstractmethod
    async def handle_webhook(
        self,
        event_type: str,
        event_data: dict[str, Any],
    ) -> None:
        """
        Processa um webhook do provider.

        Args:
            event_type: Tipo do evento
            event_data: Dados do evento
        """
        pass

    @abstractmethod
    def parse_payment_from_webhook(
        self,
        event_data: dict[str, Any],
    ) -> dict[str, Any]:
        """
        Extrai informações de pagamento do webhook.

        Args:
            event_data: Dados do webhook

        Returns:
            {
                'processor_id': str,
                'status': PaymentStatus,
                'amount': int,
                'currency': str,
                'method': str,
                'method_metadata': dict,
                'customer_email': Optional[str],
                'decline_reason': Optional[str],
                'decline_message': Optional[str],
                'risk_level': Optional[str],
                'risk_score': Optional[int],
            }
        """
        pass

    @abstractmethod
    def parse_payment_method_from_intent(
        self,
        intent_data: dict[str, Any],
    ) -> dict[str, Any]:
        """
        Extrai informações de payment method do intent.

        Args:
            intent_data: Dados do intent/order

        Returns:
            {
                'processor_id': str,
                'type': str,
                'method_metadata': dict,
            }
        """
        pass

