"""Registry centralizado de providers de pagamento."""

from typing import Dict

from polar.enums import PaymentProcessor

from .base import PaymentProvider


class PaymentProviderRegistry:
    """Registry centralizado de providers de pagamento."""

    _providers: Dict[PaymentProcessor, PaymentProvider] = {}

    @classmethod
    def register(
        cls,
        processor: PaymentProcessor,
        provider: PaymentProvider,
    ) -> None:
        """Registra um provider."""
        cls._providers[processor] = provider

    @classmethod
    def get(cls, processor: PaymentProcessor) -> PaymentProvider:
        """Retorna o provider para um processor."""
        if processor not in cls._providers:
            raise ValueError(f"No provider registered for {processor}")
        return cls._providers[processor]

    @classmethod
    def get_all(cls) -> Dict[PaymentProcessor, PaymentProvider]:
        """Retorna todos os providers registrados."""
        return cls._providers.copy()

    @classmethod
    def is_supported(cls, processor: PaymentProcessor) -> bool:
        """Verifica se um processor é suportado."""
        return processor in cls._providers

