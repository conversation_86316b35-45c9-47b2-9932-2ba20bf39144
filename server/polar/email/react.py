import subprocess
from typing import TYPE_CHECKING

from polar.config import settings

if TYPE_CHECKING:
    from .schemas import <PERSON>ail


def render_email_template(email: "Email") -> str:
    import os
    from polar.logging import Logger
    
    log = Logger()
    
    binary_path = settings.EMAIL_RENDERER_BINARY_PATH
    
    if not os.path.exists(binary_path):
        error_msg = (
            f"Email renderer binary not found at {binary_path}. "
            f"Please build it by running: uv run task emails"
        )
        log.error(error_msg)
        raise FileNotFoundError(error_msg)
    
    try:
        process = subprocess.Popen(
            [
                str(binary_path),
                email.template,
                email.props.model_dump_json(),
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
        stdout, stderr = process.communicate()
        
        if process.returncode != 0:
            stderr_text = stderr.decode('utf-8')
            error_msg = f"Error in react-email process: {stderr_text}"
            log.error(error_msg, template=email.template, returncode=process.returncode)
            raise Exception(error_msg)
        
        return stdout.decode("utf-8")
    except FileNotFoundError:
        raise
    except Exception as e:
        log.error("Error rendering email template", error=str(e), template=email.template, exc_info=True)
        raise


__all__ = ["render_email_template"]
