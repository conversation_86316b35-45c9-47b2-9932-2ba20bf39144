#!/bin/sh
set -e

# Script para iniciar o worker com health check server
cd /app/server

# Gerar JWKS
uv run python -m polar.kit.jwk ${POLAR_CURRENT_JWK_KID:-production} > ./.jwks.json

# Configurar porta do health check (usar PORT do Cloud Run que será injetado)
# O PORT é injetado automaticamente pelo Cloud Run
export dramatiq_prom_port=${PORT:-8080}
export dramatiq_prom_host=0.0.0.0

# Iniciar Dramatiq com health check
# O HealthMiddleware vai iniciar o servidor de health check em um fork
# O servidor escuta na porta dramatiq_prom_port (que é o PORT do Cloud Run)
# Converter vírgulas em espaços para o formato correto do Dramatiq
QUEUES=$(echo ${DRAMATIQ_QUEUES:-high_priority,default} | tr ',' ' ')
exec uv run dramatiq \
  -p ${DRAMATIQ_PROCESSES:-2} \
  -t ${DRAMATIQ_THREADS:-4} \
  --queues ${QUEUES} \
  -f polar.worker.scheduler:start \
  polar.worker.run

