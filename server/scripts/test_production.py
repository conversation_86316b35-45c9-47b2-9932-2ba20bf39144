"""Script to test backend and worker endpoints in production."""
import sys

import httpx
import typer

cli = typer.Typer()

BACKEND_URL = "https://polar-api-iuu5qv6jja-rj.a.run.app"
WORKER_URL = "https://polar-worker-iuu5qv6jja-rj.a.run.app"


def test_backend_health() -> bool:
    """Test backend health endpoint."""
    # Try different health check paths
    health_paths = ["/healthz", "/health", "/"]
    for path in health_paths:
        try:
            response = httpx.get(f"{BACKEND_URL}{path}", timeout=10.0)
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ Backend health check ({path}): {data}")
                except:
                    print(f"✅ Backend health check ({path}): {response.status_code}")
                return True
        except Exception:
            continue
    
    # If /healthz doesn't work, test if API is accessible via openapi
    try:
        response = httpx.get(f"{BACKEND_URL}/openapi.json", timeout=10.0)
        if response.status_code == 200:
            print(f"⚠️  Backend health check (/healthz) not found, but API is accessible")
            print(f"   OpenAPI schema loaded successfully")
            return True
    except Exception:
        pass
    
    print(f"❌ Backend health check failed")
    return False


def test_worker_health() -> bool:
    """Test worker health endpoint."""
    try:
        response = httpx.get(f"{WORKER_URL}/", timeout=10.0)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Worker health check: {data}")
            return True
        else:
            print(f"❌ Worker health check failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Worker health check error: {e}")
        return False


def test_backend_api() -> bool:
    """Test backend API endpoints."""
    endpoints = [
        "/api/v1/",
        "/openapi.json",
    ]
    
    all_passed = True
    for endpoint in endpoints:
        try:
            response = httpx.get(f"{BACKEND_URL}{endpoint}", timeout=10.0)
            if response.status_code in [200, 404]:  # 404 is ok for some endpoints
                print(f"✅ Backend {endpoint}: {response.status_code}")
            else:
                print(f"⚠️  Backend {endpoint}: {response.status_code}")
                all_passed = False
        except Exception as e:
            print(f"❌ Backend {endpoint} error: {e}")
            all_passed = False
    
    return all_passed


@cli.command()
def test_all() -> None:
    """Test both backend and worker."""
    print("🧪 Testing Production Services\n")
    
    print("=" * 50)
    print("Testing Backend...")
    print("=" * 50)
    backend_health = test_backend_health()
    backend_api = test_backend_api()
    
    print("\n" + "=" * 50)
    print("Testing Worker...")
    print("=" * 50)
    worker_health = test_worker_health()
    
    print("\n" + "=" * 50)
    print("Summary")
    print("=" * 50)
    print(f"Backend Health: {'✅' if backend_health else '❌'}")
    print(f"Backend API: {'✅' if backend_api else '❌'}")
    print(f"Worker Health: {'✅' if worker_health else '❌'}")
    
    if backend_health and backend_api and worker_health:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)


@cli.command()
def test_backend() -> None:
    """Test backend only."""
    print("🧪 Testing Backend...\n")
    backend_health = test_backend_health()
    backend_api = test_backend_api()
    
    if backend_health and backend_api:
        print("\n✅ Backend tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Backend tests failed!")
        sys.exit(1)


@cli.command()
def test_worker() -> None:
    """Test worker only."""
    print("🧪 Testing Worker...\n")
    worker_health = test_worker_health()
    
    if worker_health:
        print("\n✅ Worker tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Worker tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    cli()

