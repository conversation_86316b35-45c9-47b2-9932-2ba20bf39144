#!/usr/bin/env python3
"""
Script para testar o login via API.
Valida se o endpoint de login está funcionando corretamente.
"""
import asyncio
import json
import sys
from typing import Any

import httpx


async def test_login_request(email: str = "<EMAIL>") -> dict[str, Any]:
    """
    Testa a requisição de código de login.
    
    Args:
        email: Email para testar o login
        
    Returns:
        Dict com o resultado do teste
    """
    base_url = "http://127.0.0.1:8000"
    endpoint = f"{base_url}/v1/login-code/request"
    
    print(f"\n{'='*60}")
    print(f"🧪 TESTE DE LOGIN - {email}")
    print(f"{'='*60}\n")
    
    print(f"📡 Endpoint: {endpoint}")
    print(f"📧 Email: {email}\n")
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            # Testar CORS primeiro com OPTIONS
            print("1️⃣  Testando CORS (OPTIONS)...")
            cors_response = await client.options(
                endpoint,
                headers={
                    "Origin": "http://localhost:3000",
                    "Access-Control-Request-Method": "POST",
                    "Access-Control-Request-Headers": "content-type",
                },
            )
            
            print(f"   Status: {cors_response.status_code}")
            cors_headers = {
                k: v for k, v in cors_response.headers.items() 
                if k.lower().startswith("access-control")
            }
            
            if cors_headers:
                print(f"   ✅ CORS Headers encontrados:")
                for k, v in cors_headers.items():
                    print(f"      {k}: {v}")
            else:
                print(f"   ⚠️  Nenhum header CORS encontrado")
            
            print(f"\n2️⃣  Enviando requisição de login (POST)...")
            
            # Fazer a requisição POST
            response = await client.post(
                endpoint,
                json={"email": email},
                headers={
                    "Origin": "http://localhost:3000",
                    "Content-Type": "application/json",
                },
            )
            
            print(f"   Status: {response.status_code}")
            print(f"   Headers de resposta:")
            for k, v in response.headers.items():
                if k.lower().startswith("access-control") or k.lower() == "content-type":
                    print(f"      {k}: {v}")
            
            if response.status_code == 202:
                print(f"\n   ✅ SUCESSO! Código de login solicitado com sucesso!")
                print(f"   📝 Verifique o terminal do worker para ver o código OTP")
                return {
                    "success": True,
                    "status": response.status_code,
                    "message": "Login code requested successfully",
                    "cors_working": bool(cors_headers),
                }
            else:
                print(f"\n   ❌ ERRO! Status: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   Detalhes: {json.dumps(error_detail, indent=2)}")
                except:
                    print(f"   Resposta: {response.text[:200]}")
                
                return {
                    "success": False,
                    "status": response.status_code,
                    "message": response.text[:200],
                    "cors_working": bool(cors_headers),
                }
                
        except httpx.ConnectError:
            print(f"\n   ❌ ERRO: Não foi possível conectar ao servidor!")
            print(f"   Verifique se o servidor está rodando em {base_url}")
            return {
                "success": False,
                "status": None,
                "message": "Server not reachable",
                "cors_working": False,
            }
        except Exception as e:
            print(f"\n   ❌ ERRO INESPERADO: {type(e).__name__}")
            print(f"   {str(e)}")
            return {
                "success": False,
                "status": None,
                "message": str(e),
                "cors_working": False,
            }


async def main() -> None:
    """Função principal."""
    email = sys.argv[1] if len(sys.argv) > 1 else "<EMAIL>"
    
    result = await test_login_request(email)
    
    print(f"\n{'='*60}")
    print(f"📊 RESULTADO DO TESTE")
    print(f"{'='*60}")
    print(f"✅ Sucesso: {result['success']}")
    print(f"📡 Status: {result['status']}")
    print(f"🌐 CORS: {'✅ Funcionando' if result['cors_working'] else '❌ Não funcionando'}")
    print(f"💬 Mensagem: {result['message']}")
    print(f"{'='*60}\n")
    
    if not result['success']:
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

