# Aplicar Migrations e Criar Admin em Produção

## Problema Identificado

O script anterior (`apply_migrations_and_create_admin.py`) não funcionou porque:

1. **Carregamento de Settings**: O módulo `polar.config` car<PERSON><PERSON> as configurações no momento da importação (linha 42-44 do `config.py`). Se as variáveis de ambiente não estiverem definidas antes de importar `settings`, ele usará valores padrão ou do arquivo `.env`.

2. **Conflito de Configuração**: O script anterior tentava sobrescrever a URL do Alembic diretamente, mas o `env.py` das migrations usa `settings.get_postgres_dsn("asyncpg")` que já estava carregado com valores incorretos.

3. **Ordem de Execução**: As variáveis de ambiente precisam ser definidas ANTES de qualquer importação que use `settings`.

## Solução

O novo script `apply_migrations_and_create_admin_production.py`:

1. **Define todas as variáveis de ambiente primeiro** (antes de qualquer importação)
2. **Usa a mesma lógica do `scripts/db.py`** para aplicar migrations (garantindo compatibilidade)
3. **Usa os serviços existentes** para criar o usuário admin (mesma lógica do `create_admin_user.py`)

## Como Usar

### Opção 1: Executar o script diretamente

```bash
cd server
python scripts/apply_migrations_and_create_admin_production.py
```

### Opção 2: Executar via módulo Python

```bash
cd server
python -m scripts.apply_migrations_and_create_admin_production
```

### Opção 3: Executar via uv (se disponível)

```bash
cd server
uv run python scripts/apply_migrations_and_create_admin_production.py
```

## Configuração

O script está configurado com as credenciais do Neon (produção):

- **Host**: `ep-dry-cell-achqzu4j-pooler.sa-east-1.aws.neon.tech`
- **Database**: `neondb`
- **User**: `neondb_owner`
- **SSL**: `require`
- **Email Admin**: `<EMAIL>`

Para alterar essas configurações, edite as constantes no início do arquivo:

```python
DB_HOST = "..."
DB_PASSWORD = "..."
EMAIL = "..."
```

## O que o Script Faz

1. **Define variáveis de ambiente** para produção (Neon)
2. **Aplica todas as migrations** usando Alembic
3. **Cria ou atualiza o usuário admin** com o email especificado
4. **Garante que o usuário não está deletado ou bloqueado**
5. **Imprime informações detalhadas** sobre o progresso

## Verificação

Após executar, o script mostrará:

- ✅ Status das migrations
- ✅ ID do usuário criado/atualizado
- ✅ Confirmação de que é admin
- ✅ Status de deleted_at e blocked_at

## Troubleshooting

### Erro: "settings não carregou as variáveis corretas"

**Solução**: Certifique-se de que as variáveis de ambiente estão definidas ANTES de importar qualquer módulo. O script já faz isso automaticamente.

### Erro: "Tabela users não existe"

**Solução**: As migrations não foram aplicadas. O script deve aplicar as migrations primeiro. Se ainda houver erro, verifique se o banco está acessível.

### Erro de conexão SSL

**Solução**: Verifique se `POLAR_POSTGRES_SSLMODE=require` está definido. O script já define isso automaticamente.

### Erro: "asyncpg não encontrado" ou "psycopg2 não encontrado"

**Solução**: Instale as dependências:
```bash
cd server
uv sync  # ou pip install -r requirements.txt
```

## Alternativa: Usar Scripts Separados

Se preferir executar separadamente:

```bash
# 1. Aplicar migrations
cd server
POLAR_ENV=production \
POLAR_POSTGRES_USER=neondb_owner \
POLAR_POSTGRES_PWD=npg_iX2kVBloh1YT \
POLAR_POSTGRES_HOST=ep-dry-cell-achqzu4j-pooler.sa-east-1.aws.neon.tech \
POLAR_POSTGRES_PORT=5432 \
POLAR_POSTGRES_DATABASE=neondb \
POLAR_POSTGRES_SSLMODE=require \
python -m scripts.db upgrade

# 2. Criar admin
python -m scripts.create_admin_user <EMAIL>
```

Mas o script unificado (`apply_migrations_and_create_admin_production.py`) é mais conveniente e garante que tudo seja executado na ordem correta.

