# 📋 Resumo da Configuração do Servidor

## 🔧 Porta do Servidor
- **Porta**: `8000`
- **Host**: `127.0.0.1`
- **Configuração**: `server/pyproject.toml` (linha 87)
- **Comando**: `uvicorn polar.app:app --reload --workers 1 --host 127.0.0.1 --port 8000`

## 🌍 Variáveis de Ambiente (.env)

### Valores Encontrados:
```
POLAR_FRONTEND_BASE_URL=http://127.0.0.1:3000
POLAR_CORS_ORIGINS=["http://127.0.0.1:3000","http://localhost:3000"]
```

### <PERSON><PERSON> (config.py):
- `FRONTEND_BASE_URL`: `http://127.0.0.1:3000`
- `BASE_URL`: `http://127.0.0.1:8000`
- `CORS_ORIGINS`: `[]` (vazio)
- `ENV`: `development`

## 🔐 Configuração CORS (app.py)

### Fluxo de Configuração:
1. Lê `CORS_ORIGINS` do config (do .env ou padrão)
2. Adiciona `FRONTEND_BASE_URL` se não estiver na lista
3. **Em desenvolvimento**, adiciona variantes:
   - Se `FRONTEND_BASE_URL = http://127.0.0.1:3000` → Adiciona `http://localhost:3000`
   - Se `FRONTEND_BASE_URL = http://localhost:3000` → Adiciona `http://127.0.0.1:3000`
4. Matcher verifica se origin está na lista final
5. Em dev, também permite localhost/127.0.0.1 na mesma porta

### Origens CORS Permitidas (em desenvolvimento):
✅ `http://127.0.0.1:3000`
✅ `http://localhost:3000`
✅ Qualquer `localhost:3000` ou `127.0.0.1:3000` (mesma porta)

## 🔍 Como Verificar se Está Funcionando:

1. **Verifique os logs do servidor ao iniciar:**
   - Procure por: `"CORS allowed origins"` e `"CORS final allowed origins"`
   - Deve mostrar: `['http://127.0.0.1:3000', 'http://localhost:3000']`

2. **Teste com o script:**
   ```bash
   python scripts/test_login.py <EMAIL>
   ```

3. **Verifique no navegador:**
   - Abra o DevTools → Network
   - Tente fazer login
   - Verifique se os headers CORS estão presentes na resposta

## ⚠️ Possíveis Problemas:

1. **Servidor não está rodando na porta 8000**
   - Verifique: `ps aux | grep uvicorn`
   - Verifique: `curl http://127.0.0.1:8000/healthz`

2. **CORS não está sendo aplicado**
   - Verifique os logs do servidor
   - Verifique se `ENV=development`

3. **Erro 500 no login**
   - Verifique os logs do servidor para ver o erro específico
   - Verifique se o worker está rodando
   - Verifique se o binário de email está acessível

