"""Apply migrations and create admin user."""
import asyncio
import os
import sys
from datetime import datetime, timezone
from uuid import uuid4

import asyncpg
from alembic import command
from alembic.config import Config

# Database connection parameters from env.yaml
DB_HOST = "ep-dry-cell-achqzu4j-pooler.sa-east-1.aws.neon.tech"
DB_PORT = 5432
DB_USER = "neondb_owner"
DB_PASSWORD = "npg_iX2kVBloh1YT"
DB_NAME = "neondb"
DB_SSL = "require"

EMAIL = "<EMAIL>"


def apply_migrations() -> None:
    """Apply database migrations using Alembic."""
    print("🔄 Aplicando migrations do banco de dados...")
    
    # Set environment variables for Alembic
    os.environ["POLAR_ENV"] = "production"
    os.environ["POLAR_POSTGRES_USER"] = DB_USER
    os.environ["POLAR_POSTGRES_PWD"] = DB_PASSWORD
    os.environ["POLAR_POSTGRES_HOST"] = DB_HOST
    os.environ["POLAR_POSTGRES_PORT"] = str(DB_PORT)
    os.environ["POLAR_POSTGRES_DATABASE"] = DB_NAME
    os.environ["POLAR_POSTGRES_SSLMODE"] = DB_SSL
    
    # Get Alembic config
    config_file = os.path.join(os.path.dirname(__file__), "../alembic.ini")
    alembic_cfg = Config(config_file)
    
    # Set database URL - use psycopg2 for synchronous migrations
    # psycopg2 uses sslmode in connection string
    dsn = f"postgresql+psycopg2://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?sslmode={DB_SSL}"
    alembic_cfg.set_main_option("sqlalchemy.url", dsn.replace("%", "%%"))
    
    try:
        command.upgrade(alembic_cfg, "head")
        print("✅ Migrations aplicadas com sucesso!")
        return True
    except Exception as e:
        print(f"❌ ERRO ao aplicar migrations:")
        print(f"   {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        return False


async def create_admin_user() -> None:
    """Create admin user."""
    conn_string = (
        f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        f"?ssl=require"
    )
    
    print(f"\n🔌 Conectando ao banco de dados...")
    
    try:
        conn = await asyncpg.connect(conn_string)
        print("✅ Conectado!")
        
        # Check if user exists
        print(f"\n🔍 Verificando se usuário {EMAIL} existe...")
        user = await conn.fetchrow(
            """
            SELECT id, email, is_admin, created_at, deleted_at, blocked_at
            FROM users
            WHERE LOWER(email) = LOWER($1)
            """,
            EMAIL,
        )
        
        if user:
            user_id = user["id"]
            is_admin = user["is_admin"]
            deleted_at = user["deleted_at"]
            blocked_at = user["blocked_at"]
            
            print(f"✅ Usuário encontrado:")
            print(f"   ID: {user_id}")
            print(f"   Email: {user['email']}")
            print(f"   Admin atual: {is_admin}")
            
            needs_update = False
            if deleted_at is not None:
                print(f"\n⚠️  Usuário está deletado. Restaurando...")
                needs_update = True
            if blocked_at is not None:
                print(f"\n⚠️  Usuário está bloqueado. Desbloqueando...")
                needs_update = True
            if not is_admin:
                print(f"\n🔧 Tornando usuário admin...")
                needs_update = True
            
            if needs_update:
                await conn.execute(
                    """
                    UPDATE users
                    SET is_admin = TRUE, 
                        deleted_at = NULL, 
                        blocked_at = NULL,
                        updated_at = $1
                    WHERE id = $2
                    """,
                    datetime.now(timezone.utc),
                    user_id,
                )
                print("✅ Usuário atualizado!")
        else:
            print(f"❌ Usuário não encontrado. Criando novo usuário admin...")
            user_id = uuid4()
            now = datetime.now(timezone.utc)
            
            await conn.execute(
                """
                INSERT INTO users (
                    id, email, is_admin, email_verified, 
                    accepted_terms_of_service, identity_verification_status,
                    created_at, updated_at, meta
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                """,
                user_id,
                EMAIL,
                True,
                False,
                False,
                "unverified",
                now,
                now,
                "{}",
            )
            print(f"✅ Usuário admin criado!")
            print(f"   ID: {user_id}")
        
        # Verify
        print(f"\n🔍 Verificando resultado...")
        final_user = await conn.fetchrow(
            """
            SELECT id, email, is_admin, deleted_at, blocked_at
            FROM users
            WHERE id = $1
            """,
            user_id,
        )
        
        if final_user and final_user["is_admin"] and final_user["deleted_at"] is None and final_user["blocked_at"] is None:
            print(f"\n🎉 SUCESSO! Usuário admin criado/atualizado!")
            print(f"   Email: {EMAIL}")
            print(f"   ID: {final_user['id']}")
            sys.exit(0)
        else:
            print(f"\n❌ ERRO na verificação!")
            sys.exit(1)
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ ERRO:")
        print(f"   {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    # First apply migrations
    if not apply_migrations():
        print("\n⚠️  Migrations falharam, mas continuando...")
    
    # Then create admin user
    asyncio.run(create_admin_user())

