"""<PERSON><PERSON><PERSON> to create an admin user in production."""
import asyncio

import typer

from polar.kit.db.postgres import create_async_sessionmaker
from polar.postgres import create_async_engine
from polar.user.repository import UserRepository
from polar.user.service import user as user_service

cli = typer.Typer()


async def create_admin_user(email: str) -> None:
    """Create or update a user to be admin."""
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    async with sessionmaker() as session:
        # Get or create user
        user, created = await user_service.get_by_email_or_create(
            session=session,
            email=email,
        )
        
        user_repository = UserRepository.from_session(session)
        
        # Update user to be admin
        await user_repository.update(
            user,
            update_dict={
                "is_admin": True,
            },
        )
        
        await session.commit()
        
        if created:
            print(f"✅ Created admin user: {email}")
        else:
            print(f"✅ Updated user to admin: {email}")
        print(f"   User ID: {user.id}")
        print(f"   Is Admin: {user.is_admin}")


@cli.command()
def create_admin(
    email: str = typer.Argument(..., help="Email address for the admin user"),
) -> None:
    """Create or update a user to be admin."""
    asyncio.run(create_admin_user(email))


if __name__ == "__main__":
    cli()

