"""<PERSON>ript to create admin user by connecting directly to the database."""
import asyncio
import os
import sys
from datetime import datetime, timezone
from uuid import uuid4

import asyncpg

# Database connection parameters from env.yaml
DB_HOST = "ep-dry-cell-achqzu4j-pooler.sa-east-1.aws.neon.tech"
DB_PORT = 5432
DB_USER = "neondb_owner"
DB_PASSWORD = "npg_iX2kVBloh1YT"
DB_NAME = "neondb"
DB_SSL = "require"

EMAIL = "<EMAIL>"


async def create_admin_user() -> None:
    """Create or update user to be admin."""
    # Build connection string
    conn_string = (
        f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        f"?sslmode={DB_SSL}"
    )
    
    print(f"🔌 Conectando ao banco de dados...")
    print(f"   Host: {DB_HOST}")
    print(f"   Database: {DB_NAME}")
    
    try:
        conn = await asyncpg.connect(conn_string)
        print("✅ Conectado ao banco de dados!")
        
        # List all tables to debug
        print(f"\n🔍 Verificando tabelas no banco...")
        tables = await conn.fetch(
            """
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
            """
        )
        print(f"   Tabelas encontradas: {len(tables)}")
        for table in tables[:10]:  # Show first 10
            print(f"   - {table['table_name']}")
        if len(tables) > 10:
            print(f"   ... e mais {len(tables) - 10} tabelas")
        
        # Check if user exists
        print(f"\n🔍 Verificando se usuário {EMAIL} existe...")
        user = await conn.fetchrow(
            """
            SELECT id, email, is_admin, created_at, deleted_at, blocked_at
            FROM users
            WHERE LOWER(email) = LOWER($1)
            """,
            EMAIL,
        )
        
        if user:
            user_id = user["id"]
            is_admin = user["is_admin"]
            deleted_at = user["deleted_at"]
            blocked_at = user["blocked_at"]
            
            print(f"✅ Usuário encontrado:")
            print(f"   ID: {user_id}")
            print(f"   Email: {user['email']}")
            print(f"   Admin atual: {is_admin}")
            print(f"   Deleted: {deleted_at is not None}")
            print(f"   Blocked: {blocked_at is not None}")
            
            if deleted_at is not None:
                print(f"\n⚠️  Usuário está deletado. Restaurando...")
                await conn.execute(
                    """
                    UPDATE users
                    SET deleted_at = NULL, updated_at = $1
                    WHERE id = $2
                    """,
                    datetime.now(timezone.utc),
                    user_id,
                )
                print("✅ Usuário restaurado!")
            
            if blocked_at is not None:
                print(f"\n⚠️  Usuário está bloqueado. Desbloqueando...")
                await conn.execute(
                    """
                    UPDATE users
                    SET blocked_at = NULL, updated_at = $1
                    WHERE id = $2
                    """,
                    datetime.now(timezone.utc),
                    user_id,
                )
                print("✅ Usuário desbloqueado!")
            
            if is_admin:
                print(f"\n✅ Usuário já é admin!")
            else:
                print(f"\n🔧 Tornando usuário admin...")
                await conn.execute(
                    """
                    UPDATE users
                    SET is_admin = TRUE, updated_at = $1
                    WHERE id = $2
                    """,
                    datetime.now(timezone.utc),
                    user_id,
                )
                print("✅ Usuário atualizado para admin!")
        else:
            print(f"❌ Usuário não encontrado. Criando novo usuário admin...")
            user_id = uuid4()
            now = utc_now()
            
            await conn.execute(
                """
                INSERT INTO users (
                    id, email, is_admin, email_verified, 
                    accepted_terms_of_service, identity_verification_status,
                    created_at, updated_at, meta
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                """,
                user_id,
                EMAIL,
                True,  # is_admin
                False,  # email_verified
                False,  # accepted_terms_of_service
                "unverified",  # identity_verification_status
                now,
                now,
                "{}",  # meta (JSONB empty object)
            )
            print(f"✅ Usuário admin criado!")
            print(f"   ID: {user_id}")
        
        # Verify the user is now admin
        print(f"\n🔍 Verificando resultado final...")
        final_user = await conn.fetchrow(
            """
            SELECT id, email, is_admin, deleted_at, blocked_at
            FROM users
            WHERE id = $1
            """,
            user_id,
        )
        
        if final_user:
            print(f"✅ Verificação concluída:")
            print(f"   ID: {final_user['id']}")
            print(f"   Email: {final_user['email']}")
            print(f"   Is Admin: {final_user['is_admin']}")
            print(f"   Deleted: {final_user['deleted_at'] is not None}")
            print(f"   Blocked: {final_user['blocked_at'] is not None}")
            
            if final_user["is_admin"] and final_user["deleted_at"] is None and final_user["blocked_at"] is None:
                print(f"\n🎉 SUCESSO! Usuário admin criado/atualizado com sucesso!")
                print(f"   Email: {EMAIL}")
                print(f"   Você pode fazer login com este email no sistema.")
            else:
                print(f"\n⚠️  AVISO: Usuário criado mas pode ter problemas:")
                if not final_user["is_admin"]:
                    print(f"   - Não é admin")
                if final_user["deleted_at"] is not None:
                    print(f"   - Está deletado")
                if final_user["blocked_at"] is not None:
                    print(f"   - Está bloqueado")
                sys.exit(1)
        else:
            print(f"❌ ERRO: Não foi possível verificar o usuário após criação!")
            sys.exit(1)
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ ERRO ao conectar ou executar operação:")
        print(f"   {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(create_admin_user())

