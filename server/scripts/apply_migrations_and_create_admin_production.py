#!/usr/bin/env python3
"""
Script para aplicar migrations e criar usuário admin no banco de produção (Neon).

IMPORTANTE: Este script define as variáveis de ambiente ANTES de importar
qualquer módulo que use settings, para garantir que as configurações sejam
carregadas corretamente.
"""
import asyncio
import os
import sys
from pathlib import Path

# ============================================
# CONFIGURAÇÕES DO BANCO DE PRODUÇÃO (NEON)
# ============================================
# Estes valores devem ser definidos ANTES de importar qualquer módulo
# que use settings, pois o settings é carregado no momento da importação

DB_HOST = "ep-dry-cell-achqzu4j-pooler.sa-east-1.aws.neon.tech"
DB_PORT = 5432
DB_USER = "neondb_owner"
DB_PASSWORD = "npg_iX2kVBloh1YT"
DB_NAME = "neondb"
DB_SSL = "require"

EMAIL = "<EMAIL>"

# Definir variáveis de ambiente ANTES de qualquer importação
os.environ["POLAR_ENV"] = "production"
os.environ["POLAR_POSTGRES_USER"] = DB_USER
os.environ["POLAR_POSTGRES_PWD"] = DB_PASSWORD
os.environ["POLAR_POSTGRES_HOST"] = DB_HOST
os.environ["POLAR_POSTGRES_PORT"] = str(DB_PORT)
os.environ["POLAR_POSTGRES_DATABASE"] = DB_NAME
os.environ["POLAR_POSTGRES_SSLMODE"] = DB_SSL

# Agora podemos importar os módulos
import subprocess

import dramatiq

from polar.config import settings
from polar.kit.db.postgres import create_async_sessionmaker
from polar.postgres import create_async_engine
from polar.redis import create_redis
from polar.user.repository import UserRepository
from polar.user.service import user as user_service
from polar.worker import JobQueueManager


def apply_migrations() -> bool:
    """
    Aplica as migrations do banco de dados usando o script db.py.
    
    Usa subprocess para executar o comando em um processo separado,
    evitando problemas com event loops e configurações.
    
    Retorna True se bem-sucedido, False caso contrário.
    """
    print("🔄 Aplicando migrations do banco de dados...")
    print(f"   Host: {DB_HOST}")
    print(f"   Database: {DB_NAME}")
    print(f"   User: {DB_USER}")
    
    try:
        # Executar o comando db upgrade via subprocess
        # Isso garante que as variáveis de ambiente estejam disponíveis
        script_dir = Path(__file__).parent
        server_dir = script_dir.parent
        
        print("   Executando 'python -m scripts.db upgrade'...")
        result = subprocess.run(
            ["python", "-m", "scripts.db", "upgrade"],
            cwd=str(server_dir),
            env=os.environ.copy(),
            capture_output=True,
            text=True,
            check=False,
        )
        
        if result.returncode == 0:
            print("✅ Migrations aplicadas com sucesso!")
            if result.stdout:
                print(f"   Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ ERRO ao aplicar migrations:")
            if result.stderr:
                print(f"   {result.stderr.strip()}")
            if result.stdout:
                print(f"   {result.stdout.strip()}")
            return False
        
    except Exception as e:
        print(f"❌ ERRO ao aplicar migrations:")
        print(f"   Tipo: {type(e).__name__}")
        print(f"   Mensagem: {e}")
        import traceback
        traceback.print_exc()
        return False


async def create_admin_user() -> bool:
    """
    Cria ou atualiza o usuário admin.
    
    Retorna True se bem-sucedido, False caso contrário.
    """
    print(f"\n🔌 Conectando ao banco de dados para criar usuário admin...")
    print(f"   Email: {EMAIL}")
    
    try:
        # Criar redis e broker para JobQueueManager
        redis = create_redis("script")
        
        # Criar engine e sessionmaker usando as configurações do settings
        engine = create_async_engine("script")
        sessionmaker = create_async_sessionmaker(engine)
        
        # Inicializar JobQueueManager sem flush (os jobs serão descartados em scripts)
        # Isso permite que o código funcione sem precisar registrar todos os actors
        JobQueueManager.set()
        
        try:
            async with sessionmaker() as session:
                print("   ✅ Conectado ao banco de dados")
                
                # Verificar se o usuário já existe
                print(f"   🔍 Verificando se usuário {EMAIL} existe...")
                user, created = await user_service.get_by_email_or_create(
                    session=session,
                    email=EMAIL,
                )
                
                user_repository = UserRepository.from_session(session)
                
                # Atualizar ou criar como admin
                if created:
                    print(f"   ✅ Usuário criado")
                else:
                    print(f"   ✅ Usuário encontrado (ID: {user.id})")
                
                # Tornar admin
                await user_repository.update(
                    user,
                    update_dict={
                        "is_admin": True,
                        "deleted_at": None,  # Garantir que não está deletado
                        "blocked_at": None,  # Garantir que não está bloqueado
                    },
                )
                
                await session.commit()
                
                # Buscar novamente para confirmar
                await session.refresh(user)
                
                print(f"\n🎉 SUCESSO! Usuário admin criado/atualizado!")
                print(f"   Email: {EMAIL}")
                print(f"   ID: {user.id}")
                print(f"   Is Admin: {user.is_admin}")
                print(f"   Deleted At: {user.deleted_at}")
                print(f"   Blocked At: {user.blocked_at}")
                
                return True
        finally:
            # Cleanup
            JobQueueManager.close()
            if hasattr(redis, 'aclose'):
                await redis.aclose()
            else:
                await redis.close()
            await engine.dispose()
        
    except Exception as e:
        print(f"❌ ERRO ao criar usuário admin:")
        print(f"   Tipo: {type(e).__name__}")
        print(f"   Mensagem: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main() -> None:
    """Função principal."""
    print("=" * 60)
    print("🚀 Aplicando migrations e criando usuário admin")
    print("=" * 60)
    
    # Verificar configurações
    print("\n📋 Configurações:")
    print(f"   POLAR_ENV: {settings.ENV}")
    print(f"   Database: {settings.POSTGRES_DATABASE}")
    print(f"   Host: {settings.POSTGRES_HOST}")
    print(f"   User: {settings.POSTGRES_USER}")
    print(f"   SSL Mode: {settings.POSTGRES_SSLMODE}")
    
    # Passo 1: Aplicar migrations
    print("\n" + "=" * 60)
    migrations_ok = apply_migrations()
    
    if not migrations_ok:
        print("\n⚠️  Migrations falharam!")
        print("   Tentando continuar mesmo assim...")
        print("   (O banco pode já ter as migrations aplicadas)")
    
    # Passo 2: Criar usuário admin
    print("\n" + "=" * 60)
    admin_ok = await create_admin_user()
    
    # Resultado final
    print("\n" + "=" * 60)
    if migrations_ok and admin_ok:
        print("✅ TUDO CONCLUÍDO COM SUCESSO!")
        sys.exit(0)
    elif admin_ok:
        print("⚠️  Admin criado, mas migrations podem ter falhado")
        print("   Verifique se as migrations foram aplicadas corretamente")
        sys.exit(0)
    else:
        print("❌ ERRO: Falha ao criar usuário admin")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

