#!/usr/bin/env python3
"""
Script para verificar a configuração do servidor, CORS e variáveis de ambiente.
"""
import os
from pathlib import Path


def check_config():
    """Verifica a configuração atual."""
    print("\n" + "="*70)
    print("🔧 CONFIGURAÇÃO DO SERVIDOR")
    print("="*70)
    
    # Porta do servidor
    print("\n📡 PORTA DO SERVIDOR:")
    print("   Configurada em: server/pyproject.toml (linha 87)")
    print("   Comando: uvicorn polar.app:app --reload --workers 1 --host 127.0.0.1 --port 8000")
    print("   Porta: 8000")
    
    # Variáveis de ambiente
    print("\n🌍 VARIÁVEIS DE AMBIENTE (config.py):")
    print("   FRONTEND_BASE_URL (padrão): http://127.0.0.1:3000")
    print("   BASE_URL (padrão): http://127.0.0.1:8000")
    print("   CORS_ORIGINS (padrão): [] (vazio)")
    print("   ENV (padrão): development")
    
    # Verificar se há .env
    env_file = Path("server/.env")
    if env_file.exists():
        print(f"\n   ⚠️  Arquivo .env encontrado em: {env_file}")
        print("   Verificando variáveis CORS/FRONTEND...")
        with open(env_file) as f:
            content = f.read()
            if "FRONTEND_BASE_URL" in content:
                for line in content.split("\n"):
                    if "FRONTEND_BASE_URL" in line and not line.strip().startswith("#"):
                        print(f"      {line.strip()}")
            if "CORS_ORIGINS" in content:
                for line in content.split("\n"):
                    if "CORS_ORIGINS" in line and not line.strip().startswith("#"):
                        print(f"      {line.strip()}")
    else:
        print(f"\n   ℹ️  Nenhum arquivo .env encontrado (usando valores padrão)")
    
    # CORS Configuration
    print("\n🔐 CONFIGURAÇÃO CORS (app.py):")
    print("   1. Lê CORS_ORIGINS do config (padrão: [])")
    print("   2. Adiciona FRONTEND_BASE_URL se não estiver na lista")
    print("   3. Em desenvolvimento, adiciona variantes localhost/127.0.0.1:")
    print("      - Se FRONTEND_BASE_URL = http://127.0.0.1:3000")
    print("        → Adiciona: http://localhost:3000")
    print("      - Se FRONTEND_BASE_URL = http://localhost:3000")
    print("        → Adiciona: http://127.0.0.1:3000")
    print("   4. Matcher verifica se origin está na lista final")
    print("   5. Em dev, também permite localhost/127.0.0.1 na mesma porta")
    
    print("\n📋 ORIGENS CORS ESPERADAS (em desenvolvimento):")
    print("   ✅ http://127.0.0.1:3000")
    print("   ✅ http://localhost:3000")
    print("   ✅ Qualquer localhost:3000 ou 127.0.0.1:3000")
    
    print("\n🔍 COMO VERIFICAR:")
    print("   1. Verifique os logs do servidor ao iniciar")
    print("   2. Procure por: 'CORS allowed origins' e 'CORS final allowed origins'")
    print("   3. Teste com: python scripts/test_login.py")
    
    print("\n" + "="*70 + "\n")


if __name__ == "__main__":
    check_config()

