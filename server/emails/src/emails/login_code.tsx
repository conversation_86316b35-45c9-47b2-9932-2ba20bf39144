import { Preview, Section, Text } from '@react-email/components'
import Footer from '../components/Footer'
import IntroWithHi from '../components/IntroWithHi'
import PolarHeader from '../components/PolarHeader'
import Wrapper from '../components/Wrapper'
import type { schemas } from '../types'

export function LoginCode({
  email,
  code,
  code_lifetime_minutes,
}: schemas['LoginCodeProps']) {
  return (
    <Wrapper>
      <Preview>
        Seu código de acesso é {code}. Válido pelos próximos{' '}
        {code_lifetime_minutes.toFixed()} minutos.
      </Preview>
      <PolarHeader />
      <IntroWithHi hiMsg="Olá,">
        Aqui está seu código para acessar a Fluu Digital.{' '}
        <span className="font-bold">
          Este código é válido apenas pelos próximos {code_lifetime_minutes} minutos.
        </span>
      </IntroWithHi>
      <Section className="text-center">
        <Text className="text-brand text-5xl font-bold tracking-wider">
          {code}
        </Text>
      </Section>
      <Text className="text-gray-500">
        Se você não solicitou este email, pode ignorá-lo com segurança.
      </Text>
      <Footer email={email} />
    </Wrapper>
  )
}

LoginCode.PreviewProps = {
  email: '<EMAIL>',
  code: 'ABC123',
  code_lifetime_minutes: 30,
}

export default LoginCode
