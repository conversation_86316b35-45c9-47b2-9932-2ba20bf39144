# 🚀 Guia de Deploy do Frontend na Vercel

Este guia explica como fazer deploy do frontend Next.js na Vercel apontando para o backend no Google Cloud Run.

---

## 📋 Pré-requisitos

1. **Vercel CLI instalado**:
   ```bash
   npm i -g vercel
   ```

2. **Conta Vercel**:
   - <PERSON><PERSON><PERSON> conta em [vercel.com](https://vercel.com)
   - Fazer login: `vercel login`

3. **Backend no Google Cloud Run**:
   - URL do backend: `https://polar-api-iuu5qv6jja-rj.a.run.app`
   - Backend deve estar funcionando e acessível

---

## 🎯 Passo 1: Instalar Vercel CLI

```bash
npm i -g vercel
```

Verificar instalação:
```bash
vercel --version
```

---

## 🔐 Passo 2: Login na Vercel

```bash
vercel login
```

<PERSON><PERSON> as instruções no navegador para autenticar.

---

## 📦 Passo 3: Configurar Variáveis de Ambiente

### Opção A: Usando o Script Automatizado (Recomendado)

```bash
cd clients/apps/web
chmod +x deploy-vercel.sh
./deploy-vercel.sh
```

### Opção B: Configuração Manual

Navegue para o diretório do app:
```bash
cd clients/apps/web
```

Configure as variáveis essenciais:

```bash
# URL do backend (OBRIGATÓRIO)
vercel env add NEXT_PUBLIC_API_URL production
# Digite: https://polar-api-iuu5qv6jja-rj.a.run.app

# Variáveis básicas
vercel env add NODE_ENV production
# Digite: production

vercel env add NEXT_PUBLIC_ENVIRONMENT production
# Digite: production

vercel env add NEXT_TELEMETRY_DISABLED production
# Digite: 1

# Autenticação
vercel env add POLAR_AUTH_COOKIE_KEY production
# Digite: polar_session

vercel env add POLAR_AUTH_MCP_COOKIE_KEY production
# Digite: polar_mcp_session

vercel env add NEXT_PUBLIC_LOGIN_PATH production
# Digite: /login

# GitHub
vercel env add NEXT_PUBLIC_GITHUB_APP_NAMESPACE production
# Digite: polar-sh

vercel env add NEXT_PUBLIC_GITHUB_BADGE_EMBED_DEFAULT_LABEL production
# Digite: Fund
```

### Variáveis Opcionais

Configure estas se necessário:

```bash
# Monitoramento (Sentry)
vercel env add NEXT_PUBLIC_SENTRY_DSN production
# Digite: seu_sentry_dsn

# Analytics (PostHog)
vercel env add NEXT_PUBLIC_POSTHOG_TOKEN production
# Digite: seu_posthog_token

# Pagamentos (Stripe)
vercel env add NEXT_PUBLIC_STRIPE_KEY production
# Digite: pk_live_...

# Imagens (S3)
vercel env add S3_PUBLIC_IMAGES_BUCKET_HOSTNAME production
# Digite: seu_bucket_hostname
```

---

## 🚀 Passo 4: Fazer Deploy

### Deploy de Produção

```bash
cd clients/apps/web
vercel --prod
```

### Deploy de Preview (Teste)

```bash
vercel
```

Isso criará uma URL de preview para testar antes de fazer deploy em produção.

---

## ✅ Passo 5: Configurar CORS no Backend

Após o deploy, você receberá uma URL da Vercel (ex: `https://polar-frontend.vercel.app`).

**IMPORTANTE**: Você precisa atualizar o CORS no backend do Google Cloud Run para aceitar requests da nova URL do frontend.

### Opção 1: Via Google Cloud Console

1. Acesse [Google Cloud Console](https://console.cloud.google.com)
2. Vá para Cloud Run → polar-api
3. Edite o serviço
4. Na seção de variáveis de ambiente, atualize:
   - `POLAR_FRONTEND_BASE_URL`: URL da Vercel
   - `POLAR_CORS_ORIGINS`: `["https://sua-url.vercel.app", "https://polar-api-iuu5qv6jja-rj.a.run.app"]`
   - `POLAR_ALLOWED_HOSTS`: `["polar-api-iuu5qv6jja-rj.a.run.app", "sua-url.vercel.app"]`

### Opção 2: Via gcloud CLI

```bash
# Obter URL do deploy
FRONTEND_URL=$(vercel ls | grep production | awk '{print $2}')

# Atualizar variáveis no Cloud Run
gcloud run services update polar-api \
  --region southamerica-east1 \
  --update-env-vars POLAR_FRONTEND_BASE_URL=$FRONTEND_URL \
  --update-env-vars POLAR_CORS_ORIGINS="[\"$FRONTEND_URL\",\"https://polar-api-iuu5qv6jja-rj.a.run.app\"]" \
  --update-env-vars POLAR_ALLOWED_HOSTS="[\"polar-api-iuu5qv6jja-rj.a.run.app\",\"$FRONTEND_URL\"]"
```

---

## 🔄 Passo 6: Atualizar Variável no Frontend

Após obter a URL do frontend na Vercel, atualize a variável:

```bash
cd clients/apps/web
vercel env add NEXT_PUBLIC_FRONTEND_BASE_URL production
# Digite: https://sua-url.vercel.app
```

E faça um novo deploy:

```bash
vercel --prod
```

---

## 📊 Verificar Deploy

### Listar Deploys

```bash
vercel ls
```

### Ver Logs

```bash
vercel logs
```

### Abrir no Navegador

```bash
vercel open
```

---

## 🛠️ Comandos Úteis

### Listar Variáveis de Ambiente

```bash
vercel env ls
```

### Remover Variável

```bash
vercel env rm NOME_DA_VARIAVEL production
```

### Ver Detalhes do Projeto

```bash
vercel inspect
```

### Remover Deploy

```bash
vercel remove
```

---

## 🔍 Troubleshooting

### Erro: "Build failed"

- Verifique se todas as dependências estão instaladas
- O build command está configurado em `vercel.json`: `cd ../.. && turbo run build --filter=web`

### Erro: "API URL not found"

- Verifique se `NEXT_PUBLIC_API_URL` está configurada
- Verifique se o backend está acessível: `curl https://polar-api-iuu5qv6jja-rj.a.run.app/healthz`

### Erro: CORS

- Verifique se o backend está configurado para aceitar requests da URL da Vercel
- Veja Passo 5 acima

### Erro: "Module not found"

- Execute `pnpm install` localmente primeiro
- Verifique se o monorepo está configurado corretamente

---

## 📝 Checklist Final

- [ ] Vercel CLI instalado e autenticado
- [ ] Variáveis de ambiente configuradas
- [ ] Deploy realizado com sucesso
- [ ] URL do frontend obtida
- [ ] CORS configurado no backend
- [ ] `NEXT_PUBLIC_FRONTEND_BASE_URL` atualizada
- [ ] Aplicação funcionando corretamente

---

## 🎉 Pronto!

Sua aplicação está rodando na Vercel e conectada ao backend no Google Cloud Run!

**URL do Frontend**: `https://sua-url.vercel.app`  
**URL do Backend**: `https://polar-api-iuu5qv6jja-rj.a.run.app`

