# ⚙️ Configurar Root Directory no Vercel

## ⚠️ Problema

Se você receber este erro:
```
Error: No Next.js version detected. Make sure your package.json has "next" in either "dependencies" or "devDependencies".
```

Ou:
```
Error: The provided path ".../clients/apps/web/clients/apps/web" does not exist.
```

É porque o **Root Directory** não está configurado corretamente no Vercel.

## ✅ Solução

### Passo 1: Acessar Configurações do Projeto

1. Acesse: **https://vercel.com/ismael-costas-projects/web/settings**
2. Vá em **Settings** → **General**
3. Role até a seção **Root Directory**

### Passo 2: Configurar Root Directory

Configure o Root Directory como:

```
clients/apps/web
```

**IMPORTANTE**: 
- Use o caminho relativo à raiz do repositório git
- NÃO use caminho absoluto (ex: `/Users/<USER>/...`)
- NÃO use `~/Documents/...`

### Passo 3: <PERSON><PERSON> e <PERSON>er Deploy

1. Clique em **Save**
2. Volte ao terminal e execute:

```bash
cd clients/apps/web
vercel --prod
```

## 🔍 Verificar se está Correto

O Root Directory deve estar configurado como:
- ✅ **Correto**: `clients/apps/web`
- ❌ **Errado**: `/Users/<USER>/Documents/www/Gateways/polar/clients/apps/web`
- ❌ **Errado**: `~/Documents/www/Gateways/polar/clients/apps/web`
- ❌ **Errado**: `.` (ponto)
- ❌ **Errado**: (vazio)

## 📋 Estrutura do Repositório

```
polar/                          # Raiz do repositório git
└── clients/                    # Workspace pnpm
    ├── package.json
    ├── pnpm-workspace.yaml
    └── apps/
        └── web/                # ← Root Directory deve apontar aqui
            ├── package.json    # (tem "next": "16.0.1")
            ├── vercel.json
            └── .vercel/
```

## 🚀 Após Configurar

Depois de configurar o Root Directory corretamente:

```bash
cd clients/apps/web
vercel --prod
```

O deploy deve funcionar corretamente!

