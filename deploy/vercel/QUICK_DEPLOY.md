# 🚀 Deploy Rápido na Vercel

## ⚠️ IMPORTANTE: Configurar Root Directory

O Vercel precisa saber que o Root Directory é `clients/apps/web` para detectar o Next.js corretamente.

### Opção A: Via Dashboard (Recomendado)

1. Acesse: https://vercel.com/ismael-costas-projects/web/settings
2. Vá em **Settings** → **General**
3. Em **Root Directory**, configure: `clients/apps/web`
4. Salve as alterações

### Opção B: Re-linkar o Projeto

```bash
cd clients/apps/web
rm -rf .vercel  # Se já existir
vercel link
```

Quando solicitado:
- **Escolha**: Usar projeto existente `web`
- **Root Directory**: Confirme como `.` (diretório atual - `clients/apps/web`)

## Passos Rápidos

### 1. Linkar o Projeto (se ainda não linkado)

```bash
cd clients/apps/web
vercel link
```

Quando solicitado:
- **Escolha**: Criar um novo projeto OU usar um existente
- **Nome do projeto**: `polar-frontend` (ou o nome que preferir)
- **Root Directory**: Confirme como `.` (diretório atual)

### 2. Configurar Variáveis de Ambiente

Execute estes comandos para configurar as variáveis essenciais:

```bash
# URL do backend (OBRIGATÓRIO)
echo "https://polar-api-iuu5qv6jja-rj.a.run.app" | vercel env add NEXT_PUBLIC_API_URL production

# Variáveis básicas
echo "production" | vercel env add NODE_ENV production
echo "production" | vercel env add NEXT_PUBLIC_ENVIRONMENT production
echo "1" | vercel env add NEXT_TELEMETRY_DISABLED production

# Autenticação
echo "polar_session" | vercel env add POLAR_AUTH_COOKIE_KEY production
echo "polar_mcp_session" | vercel env add POLAR_AUTH_MCP_COOKIE_KEY production
echo "/login" | vercel env add NEXT_PUBLIC_LOGIN_PATH production

# GitHub
echo "polar-sh" | vercel env add NEXT_PUBLIC_GITHUB_APP_NAMESPACE production
echo "Fund" | vercel env add NEXT_PUBLIC_GITHUB_BADGE_EMBED_DEFAULT_LABEL production
```

### 3. Verificar Configuração

Certifique-se de que:
- ✅ O Root Directory está configurado como `clients/apps/web` no dashboard
- ✅ O `vercel.json` tem `framework: "nextjs"` configurado
- ✅ O `package.json` tem `"next": "16.0.1"` nas dependências

### 4. Fazer Deploy

```bash
vercel --prod
```

**Se der erro "No Next.js version detected":**
1. Verifique o Root Directory no dashboard da Vercel
2. Execute: `./fix-vercel-deploy.sh` (script criado)
3. Ou acesse: https://vercel.com/ismael-costas-projects/web/settings e configure Root Directory

### 5. Após o Deploy

Após o deploy, você receberá uma URL (ex: `https://polar-frontend.vercel.app`).

**Atualize a variável do frontend:**

```bash
# Substitua pela URL que você recebeu
echo "https://sua-url.vercel.app" | vercel env add NEXT_PUBLIC_FRONTEND_BASE_URL production

# Fazer novo deploy para aplicar
vercel --prod
```

**Configure CORS no Backend:**

Atualize as variáveis no Google Cloud Run:

```bash
# Substitua pela URL da Vercel
FRONTEND_URL="https://sua-url.vercel.app"

gcloud run services update polar-api \
  --region southamerica-east1 \
  --update-env-vars POLAR_FRONTEND_BASE_URL=$FRONTEND_URL \
  --update-env-vars POLAR_CORS_ORIGINS="[\"$FRONTEND_URL\",\"https://polar-api-iuu5qv6jja-rj.a.run.app\"]" \
  --update-env-vars POLAR_ALLOWED_HOSTS="[\"polar-api-iuu5qv6jja-rj.a.run.app\",\"$FRONTEND_URL\"]"
```

---

## ✅ Script Completo (Copiar e Colar)

```bash
cd clients/apps/web

# Linkar projeto (siga as instruções interativas)
vercel link

# Configurar variáveis
echo "https://polar-api-iuu5qv6jja-rj.a.run.app" | vercel env add NEXT_PUBLIC_API_URL production
echo "production" | vercel env add NODE_ENV production
echo "production" | vercel env add NEXT_PUBLIC_ENVIRONMENT production
echo "1" | vercel env add NEXT_TELEMETRY_DISABLED production
echo "polar_session" | vercel env add POLAR_AUTH_COOKIE_KEY production
echo "polar_mcp_session" | vercel env add POLAR_AUTH_MCP_COOKIE_KEY production
echo "/login" | vercel env add NEXT_PUBLIC_LOGIN_PATH production
echo "polar-sh" | vercel env add NEXT_PUBLIC_GITHUB_APP_NAMESPACE production
echo "Fund" | vercel env add NEXT_PUBLIC_GITHUB_BADGE_EMBED_DEFAULT_LABEL production

# Deploy
vercel --prod
```

---

## 🔍 Verificar Deploy

```bash
# Ver URLs
vercel ls

# Ver logs
vercel logs

# Abrir no navegador
vercel open
```

