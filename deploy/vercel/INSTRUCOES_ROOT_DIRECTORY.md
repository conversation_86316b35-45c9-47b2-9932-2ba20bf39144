# 🔧 CORREÇÃO URGENTE: Root Directory

## ❌ Erro Atual

```
Error: The provided path "~/Documents/www/Gateways/polar/clients/apps/web/clients/apps/web" does not exist.
```

O Root Directory está configurado **INCORRETAMENTE** no dashboard da Vercel.

## ✅ SOLUÇÃO (FAÇA AGORA)

### Passo 1: Acessar Dashboard

Abra no navegador:
**https://vercel.com/ismael-costas-projects/web/settings**

### Passo 2: Configurar Root Directory

1. Vá em **Settings** → **General**
2. Role até **Root Directory**
3. **DELETE** o valor atual (está errado)
4. Digite **EXATAMENTE**:
   ```
   clients/apps/web
   ```
5. **NÃO** use:
   - ❌ Caminhos absolutos
   - ❌ `~/Documents/...`
   - ❌ `/Users/<USER>/...`
   - ❌ `.` (ponto)
   - ❌ Deixar vazio

### Passo 3: Salvar

Clique em **Save** (botão no topo da página)

### Passo 4: Deploy Novamente

Depois de salvar, execute:

```bash
cd clients/apps/web
vercel --prod
```

## 📸 Como Deve Ficar

Na página de Settings, a seção Root Directory deve mostrar:

```
Root Directory
clients/apps/web
```

**IMPORTANTE**: O caminho é relativo à raiz do repositório Git, não um caminho absoluto do sistema.

## 🔍 Verificar se Está Correto

Se você ainda receber o erro após configurar, verifique:
1. O caminho está exatamente como `clients/apps/web` (sem barras no início)
2. Não há espaços extras
3. Você salvou as alterações (clique em Save)

## 🚨 Se Ainda Não Funcionar

1. Remova o link atual:
   ```bash
   cd clients/apps/web
   rm -rf .vercel
   ```

2. Faça um novo link:
   ```bash
   vercel link
   ```
   Quando perguntar sobre Root Directory, digite: `clients/apps/web`

3. Faça deploy:
   ```bash
   vercel --prod
   ```

