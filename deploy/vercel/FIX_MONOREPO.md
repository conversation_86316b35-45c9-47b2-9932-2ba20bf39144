# 🔧 Correção: Vercel + Monorepo pnpm

## Problema

O Vercel não está detectando o Next.js porque o projeto está em um monorepo e o Root Directory precisa ser configurado corretamente.

**Erro:**
```
Error: No Next.js version detected. Make sure your package.json has "next" in either "dependencies" or "devDependencies".
```

## Solução

### Opção 1: Configurar Root Directory via Dashboard (Recomendado)

1. Acesse: https://vercel.com/ismael-costas-projects/web/settings
2. Vá em **Settings** → **General**
3. Em **Root Directory**, configure: `clients/apps/web`
4. Salve as alterações
5. Faça um novo deploy: `vercel --prod`

### Opção 2: Configurar via CLI

```bash
cd clients/apps/web

# Verificar configuração atual
vercel project ls

# O Root Directory já está configurado como o diretório atual
# Se não estiver, você precisa fazer o link novamente:
rm -rf .vercel
vercel link
# Quando perguntar sobre Root Directory, confirme: . (ponto - diretório atual)
```

### Opção 3: Verificar se o vercel.json está correto

O `vercel.json` já está configurado com:
- `installCommand`: `cd ../.. && pnpm install` (instala dependências do monorepo)
- `buildCommand`: `cd ../.. && pnpm run build --filter=web` (build do app web)
- `framework`: `nextjs` (força detecção do Next.js)

### Verificar se Next.js está no package.json

```bash
cd clients/apps/web
grep -A 2 '"next"' package.json
```

Deve mostrar:
```json
"next": "16.0.1",
```

## Comandos para Testar

```bash
cd clients/apps/web

# Verificar estrutura
ls -la package.json
grep "next" package.json

# Verificar configuração do Vercel
cat vercel.json

# Fazer deploy novamente
vercel --prod
```

## Se ainda não funcionar

1. **Verificar se o Root Directory está correto no dashboard**
2. **Garantir que o projeto está linkado corretamente:**
   ```bash
   cd clients/apps/web
   vercel link
   ```

3. **Limpar cache e fazer deploy novamente:**
   ```bash
   vercel --prod --force
   ```

## Estrutura Correta

```
polar/                    # Raiz do repositório
└── clients/              # Raiz do workspace pnpm
    ├── package.json      # Workspace root
    ├── pnpm-workspace.yaml
    └── apps/
        └── web/          # App Next.js (Root Directory no Vercel)
            ├── package.json  # Tem "next": "16.0.1"
            ├── vercel.json
            └── .vercel/
```

