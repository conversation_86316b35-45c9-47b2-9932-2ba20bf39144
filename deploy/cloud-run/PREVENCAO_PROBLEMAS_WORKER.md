# Prevenção de Problemas no Worker de Email

## 🎯 Objetivo

Este documento descreve medidas preventivas para evitar que problemas no worker de email voltem a acontecer.

## 📋 Checklist de Prevenção

### 1. Validação no CI/CD

Adicionar validações automáticas no pipeline de deploy:

```yaml
# .github/workflows/deploy-worker.yml (exemplo)
- name: Validate Worker Configuration
  run: |
    # Verificar formato das filas
    if ! grep -q "tr ',' ' '" server/start-worker.sh; then
      echo "❌ ERRO: Script não converte vírgulas em espaços!"
      exit 1
    fi
    
    # Verificar se variáveis necessárias estão no env-worker.yaml
    required_vars=("POLAR_EMAIL_SENDER" "POLAR_RESEND_API_KEY" "DRAMATIQ_QUEUES")
    for var in "${required_vars[@]}"; do
      if ! grep -q "^${var}:" deploy/cloud-run/env-worker.yaml; then
        echo "❌ ERRO: Variável ${var} não encontrada!"
        exit 1
      fi
    done
```

### 2. Testes Automatizados

Criar testes que verificam:

```python
# tests/test_worker_config.py
def test_worker_script_converts_commas():
    """Verifica que o script converte vírgulas em espaços"""
    with open("server/start-worker.sh") as f:
        content = f.read()
        assert "tr ',' ' '" in content or "QUEUES=" in content

def test_worker_env_vars():
    """Verifica que variáveis necessárias estão configuradas"""
    # Verificar env-worker.yaml
    pass
```

### 3. Monitoramento e Alertas

Configurar alertas no Google Cloud Monitoring:

```yaml
# alert-policy.yaml
displayName: "Worker não processa tarefas"
conditions:
  - displayName: "Fila high_priority > 50 tarefas por 5 minutos"
    conditionThreshold:
      filter: 'resource.type="cloud_run_revision" AND resource.labels.service_name="fluu-worker"'
      comparison: COMPARISON_GT
      thresholdValue: 50
      duration: 300s
```

### 4. Documentação no Código

Adicionar comentários explicativos:

```bash
# server/start-worker.sh
# IMPORTANTE: Dramatiq espera filas separadas por ESPAÇO, não vírgula
# A variável DRAMATIQ_QUEUES pode vir com vírgulas (ex: "high_priority,default")
# Por isso convertemos para espaços antes de passar para o Dramatiq
QUEUES=$(echo ${DRAMATIQ_QUEUES:-high_priority,default} | tr ',' ' ')
```

### 5. Validação no Script de Deploy

Adicionar validações no `deploy-worker.sh`:

```bash
# deploy/cloud-run/deploy-worker.sh

# Validar script antes do deploy
echo "🔍 Validando configuração..."

# Verificar se script converte vírgulas
if ! grep -q "tr ',' ' '" server/start-worker.sh; then
  echo "❌ ERRO: Script start-worker.sh não converte vírgulas em espaços!"
  echo "   Adicione: QUEUES=\$(echo \${DRAMATIQ_QUEUES} | tr ',' ' ')"
  exit 1
fi

# Verificar variáveis de ambiente
if [ ! -f "deploy/cloud-run/env-worker.yaml" ]; then
  echo "❌ ERRO: env-worker.yaml não encontrado"
  exit 1
fi

# Verificar variáveis críticas
if ! grep -q "POLAR_EMAIL_SENDER:" deploy/cloud-run/env-worker.yaml; then
  echo "⚠️  AVISO: POLAR_EMAIL_SENDER não encontrado em env-worker.yaml"
fi

if ! grep -q "POLAR_RESEND_API_KEY:" deploy/cloud-run/env-worker.yaml; then
  echo "⚠️  AVISO: POLAR_RESEND_API_KEY não encontrado em env-worker.yaml"
fi

echo "✅ Validação concluída"
```

### 6. Health Check Melhorado

Adicionar endpoint de health check que verifica:

```python
# server/polar/worker/_health.py

async def health_detailed(request: Request) -> JSONResponse:
    """Health check detalhado que verifica:
    - Conexão Redis
    - Tamanho das filas
    - Última tarefa processada
    """
    checks = {
        "redis": check_redis_connection(),
        "queues": {
            "high_priority": get_queue_size("high_priority"),
            "default": get_queue_size("default"),
        },
        "last_processed": get_last_processed_time(),
    }
    return JSONResponse(checks)
```

### 7. Logging Estruturado

Melhorar logs para facilitar diagnóstico:

```python
# server/polar/worker/__init__.py

def before_worker_boot(self, broker, worker):
    log.info(
        "worker.starting",
        queues=settings.DRAMATIQ_QUEUES,
        processes=settings.DRAMATIQ_PROCESSES,
        threads=settings.DRAMATIQ_THREADS,
    )
```

### 8. Documentação de Troubleshooting

Manter documentação atualizada:

- `deploy/cloud-run/GUIA_RESOLUCAO_WORKER_EMAIL.md` - Guia completo
- `deploy/cloud-run/PROMPT_AGENTE_IA_WORKER_EMAIL.md` - Prompt para IA
- README com seção de troubleshooting

### 9. Revisão de Código

Checklist para PRs que afetam o worker:

- [ ] Script `start-worker.sh` converte vírgulas em espaços?
- [ ] Variáveis de ambiente estão documentadas?
- [ ] Testes cobrem cenários de falha?
- [ ] Logs são informativos o suficiente?
- [ ] Health check funciona corretamente?

### 10. Testes de Integração

Criar testes que simulam o ambiente de produção:

```python
# tests/integration/test_worker_email.py

def test_worker_processes_email_queue():
    """Testa se o worker processa tarefas de email corretamente"""
    # Enfileirar tarefa de teste
    # Aguardar processamento
    # Verificar se email foi enviado
    pass
```

## 🔄 Processo de Deploy Seguro

### Antes do Deploy

1. ✅ Validar configuração
2. ✅ Executar testes
3. ✅ Verificar que imagem Docker será rebuildada
4. ✅ Revisar mudanças no código

### Durante o Deploy

1. ✅ Monitorar logs em tempo real
2. ✅ Verificar inicialização do worker
3. ✅ Confirmar que filas estão sendo escutadas

### Após o Deploy

1. ✅ Validar que worker está processando
2. ✅ Monitorar por 10-15 minutos
3. ✅ Verificar se tamanho das filas diminui
4. ✅ Testar envio de email real

## 📊 Métricas de Monitoramento

Configurar dashboards para monitorar:

- Tamanho das filas (high_priority e default)
- Taxa de processamento (tarefas/minuto)
- Taxa de erro
- Tempo de processamento médio
- Conexões Redis ativas

## 🚨 Alertas Recomendados

1. **Fila muito grande**: > 100 tarefas por > 10 minutos
2. **Worker inativo**: Nenhuma tarefa processada por > 5 minutos
3. **Taxa de erro alta**: > 10% de falhas
4. **Conexão Redis perdida**: Erros de conexão
5. **Worker não inicia**: Falhas de inicialização

## 📝 Checklist de Manutenção Periódica

Executar mensalmente:

- [ ] Revisar logs de erros
- [ ] Verificar tamanho das filas
- [ ] Validar que variáveis de ambiente estão corretas
- [ ] Testar envio de email
- [ ] Verificar que imagem Docker está atualizada
- [ ] Revisar documentação

## 🎓 Treinamento

Garantir que a equipe conhece:

1. Como diagnosticar problemas do worker
2. Onde encontrar logs
3. Como verificar configuração
4. Processo de deploy seguro
5. Como usar os scripts de diagnóstico

## 🔗 Links Úteis

- [Guia de Resolução](./GUIA_RESOLUCAO_WORKER_EMAIL.md)
- [Prompt para Agente IA](./PROMPT_AGENTE_IA_WORKER_EMAIL.md)
- [Scripts de Diagnóstico](./diagnose-worker.sh)

---

**Última atualização**: 2025-11-08
**Mantido por**: Equipe de DevOps

