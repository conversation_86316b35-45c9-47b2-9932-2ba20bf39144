# 📋 Plano de Deploy - Polar no Google Cloud Run

## 📊 Análise do Projeto

### Stack Tecnológica

- **Backend**: Python 3.14 + FastAPI + Uvicorn
- **Database**: PostgreSQL (Neon) com SSL
- **Cache/Queue**: Redis (Upstash) com TLS
- **Worker**: Dramatiq (assíncrono)
- **Container**: <PERSON>er (multi-stage build)
- **Build Tool**: uv (Python package manager)

### Arquitetura

```
┌─────────────────┐
│  Google Cloud   │
│     Run         │
│                 │
│  ┌───────────┐  │
│  │ Polar API │  │
│  │ (FastAPI) │  │
│  └─────┬─────┘  │
│        │        │
└────────┼────────┘
         │
    ┌────┴────┐
    │         │
┌───▼───┐ ┌──▼────┐
│ Neon  │ │Upstash│
│   PG  │ │ Redis │
└───────┘ └───────┘
```

### Portas e Endpoints

- **API Port**: 10000 (interno no container)
- **Health Check**: `/healthz`
- **API Base**: `/v1/*`

## 🔧 Modificações Realizadas

### 1. Suporte a Redis TLS (Upstash)

**Arquivo**: `server/polar/config.py`

Adicionadas variáveis:
- `REDIS_PASSWORD`: Senha do Redis
- `REDIS_USERNAME`: Usuário do <PERSON>is
- `REDIS_SSL`: Flag para habilitar TLS

A propriedade `redis_url` agora constrói URLs com `rediss://` quando SSL está habilitado.

### 2. Suporte a PostgreSQL SSL (Neon)

**Arquivo**: `server/polar/config.py`

Adicionada variável:
- `POSTGRES_SSLMODE`: Modo SSL (require, prefer, etc)

O método `get_postgres_dsn` agora adiciona query parameters SSL quando necessário.

## 📦 Arquivos Criados

### 1. Scripts de Deploy

- **`deploy.sh`**: Script automatizado de deploy
  - Build da imagem Docker
  - Push para Google Container Registry
  - Deploy no Cloud Run
  - Configuração automática de variáveis

- **`parse-connections.sh`**: Script auxiliar para parsear URLs de conexão
  - Extrai credenciais de URLs Redis e PostgreSQL
  - Gera variáveis de ambiente formatadas

### 2. Configuração

- **`env.yaml`**: Arquivo de variáveis de ambiente para Cloud Run
  - Todas as variáveis necessárias documentadas
  - Valores pré-configurados com suas URLs fornecidas
  - Seções organizadas por categoria

### 3. Documentação

- **`README.md`**: Guia completo de deploy
  - Pré-requisitos
  - Passo a passo
  - Troubleshooting
  - Monitoramento

- **`DEPLOYMENT_PLAN.md`**: Este arquivo
  - Análise do projeto
  - Plano de implementação
  - Checklist

## 🚀 Plano de Execução

### Fase 1: Preparação ✅

- [x] Análise do projeto
- [x] Identificação de dependências
- [x] Modificações no código para suportar TLS/SSL
- [x] Criação de scripts de deploy
- [x] Documentação

### Fase 2: Configuração

- [ ] Configurar variáveis de ambiente obrigatórias
- [ ] Gerar chaves de segurança
- [ ] Configurar AWS S3 (se necessário)
- [ ] Configurar provider de email (Resend)
- [ ] Testar conexões com bancos de dados

### Fase 3: Build e Push

- [ ] Build da imagem Docker
- [ ] Push para Google Container Registry
- [ ] Verificar imagem no registry

### Fase 4: Deploy

- [ ] Deploy inicial no Cloud Run
- [ ] Verificar health check
- [ ] Executar migrações de banco de dados
- [ ] Testar endpoints principais

### Fase 5: Produção

- [ ] Configurar domínio customizado (opcional)
- [ ] Configurar monitoramento
- [ ] Configurar alertas
- [ ] Documentar URLs e credenciais finais

## 🔐 Segurança

### Variáveis Sensíveis

Todas as variáveis sensíveis devem ser configuradas via:
1. **Google Secret Manager** (recomendado para produção)
2. **Variáveis de ambiente** no Cloud Run (para desenvolvimento)

### Checklist de Segurança

- [ ] `POLAR_SECRET`: Chave aleatória segura gerada
- [ ] `POLAR_JWKS`: Arquivo de chaves JWK configurado
- [ ] Credenciais de banco de dados não expostas
- [ ] CORS configurado corretamente
- [ ] Allowed hosts configurado
- [ ] SSL/TLS habilitado para todas as conexões

## 📊 Recursos Cloud Run

### Configuração Inicial

```yaml
Memory: 2Gi
CPU: 2
Port: 10000
Timeout: 300s
Concurrency: 80
Min Instances: 1
Max Instances: 10
```

### Estimativa de Custos

Baseado em:
- 1 instância mínima rodando 24/7
- 2 CPU, 2Gi RAM
- Tráfego médio

**Estimativa mensal**: ~$50-100 USD (varia por região e uso)

## 🧪 Testes

### Testes Locais

Antes do deploy, teste localmente:

```bash
cd server
POLAR_ENV=production \
POLAR_POSTGRES_USER=... \
POLAR_POSTGRES_PWD=... \
POLAR_REDIS_HOST=... \
POLAR_REDIS_SSL=true \
uv run uvicorn polar.app:app --host 0.0.0.0 --port 10000
```

### Testes Pós-Deploy

1. Health check: `GET /healthz`
2. API endpoints: `GET /v1/...`
3. Conexões de banco: Verificar logs
4. Worker: Verificar processamento de jobs

## 📈 Monitoramento

### Métricas Importantes

- **Requisições por segundo**
- **Latência p50, p95, p99**
- **Taxa de erro**
- **Uso de CPU/Memória**
- **Tempo de resposta do banco de dados**

### Alertas Recomendados

- Erro rate > 1%
- Latência p95 > 1s
- CPU usage > 80%
- Memory usage > 90%
- Instâncias > 5 (para monitorar custos)

## 🔄 CI/CD (Opcional)

Para automatizar deploys:

```yaml
# .github/workflows/deploy.yml
name: Deploy to Cloud Run
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: google-github-actions/setup-gcloud@v1
      - run: ./deploy/cloud-run/deploy.sh
```

## 📝 Próximos Passos

1. **Revisar e configurar** todas as variáveis em `env.yaml`
2. **Gerar chaves de segurança** necessárias
3. **Executar o script de deploy** pela primeira vez
4. **Verificar** health check e logs
5. **Executar migrações** de banco de dados
6. **Testar** endpoints principais
7. **Configurar monitoramento** e alertas

## ✅ Checklist Final

- [x] Análise do projeto completa
- [x] Modificações no código para TLS/SSL
- [x] Scripts de deploy criados
- [x] Documentação criada
- [ ] Variáveis de ambiente configuradas
- [ ] Deploy inicial realizado
- [ ] Migrações executadas
- [ ] Testes realizados
- [ ] Monitoramento configurado
- [ ] Documentação finalizada

---

**Última atualização**: $(date)
**Versão**: 1.0.0

