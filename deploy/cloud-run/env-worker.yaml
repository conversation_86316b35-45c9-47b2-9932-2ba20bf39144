# Variáveis de ambiente para Worker (Dramatiq) no Google Cloud Run
# IMPORTANTE: Substitua os valores abaixo com suas credenciais reais

# ============================================
# CONFIGURAÇÕES BÁSICAS
# ============================================
POLAR_ENV: production
POLAR_LOG_LEVEL: INFO
POLAR_TESTING: "0"
POLAR_DEBUG: "0"

# ============================================
# DATABASE (Neon PostgreSQL)
# ============================================
POLAR_POSTGRES_USER: neondb_owner
POLAR_POSTGRES_PWD: npg_iX2kVBloh1YT
POLAR_POSTGRES_HOST: ep-dry-cell-achqzu4j-pooler.sa-east-1.aws.neon.tech
POLAR_POSTGRES_PORT: "5432"
POLAR_POSTGRES_DATABASE: neondb
POLAR_POSTGRES_SSLMODE: require

# ============================================
# REDIS (Upstash)
# ============================================
POLAR_REDIS_HOST: joint-hookworm-6489.upstash.io
POLAR_REDIS_PORT: "6379"
POLAR_REDIS_DB: "0"
POLAR_REDIS_PASSWORD: ARlZAAImcDI5MzliMzdlMTQwZGQ0YWRjYWZlMWI4NWJkNDI1Y2RjNXAyNjQ4OQ
POLAR_REDIS_USERNAME: default
POLAR_REDIS_SSL: "true"

# ============================================
# WORKER CONFIGURATION
# ============================================
# Processos e threads do Dramatiq
# -p: número de processos
# -t: número de threads por processo
DRAMATIQ_PROCESSES: "2"
DRAMATIQ_THREADS: "4"

# Queues para processar (separadas por vírgula)
DRAMATIQ_QUEUES: "high_priority,default"

# Porta para health check (Dramatiq expõe métricas)
# IMPORTANTE: Usar PORT do Cloud Run para health check
# O HealthMiddleware do Dramatiq expõe / endpoint na porta dramatiq_prom_port
dramatiq_prom_host: "0.0.0.0"
dramatiq_prom_port: "8080"  # Cloud Run injeta PORT=8080, mas podemos usar variável

# ============================================
# SEGURANÇA
# ============================================
POLAR_SECRET: 5HixFq0des4UY6YO1TMFthD_I0Ari3dAfmkh_54C_S8
POLAR_CURRENT_JWK_KID: production
POLAR_JWKS: ./.jwks.json

# ============================================
# AWS S3 (para armazenamento de arquivos)
# ============================================
POLAR_AWS_ACCESS_KEY_ID: YOUR_AWS_ACCESS_KEY
POLAR_AWS_SECRET_ACCESS_KEY: YOUR_AWS_SECRET_KEY
POLAR_AWS_REGION: sa-east-1
POLAR_AWS_SIGNATURE_VERSION: v4
POLAR_S3_FILES_BUCKET_NAME: polar-files
POLAR_S3_FILES_PUBLIC_BUCKET_NAME: polar-public-files
POLAR_S3_FILES_DOWNLOAD_SALT: na8nJcdXC5n312-SnsFO-Q
POLAR_S3_FILES_DOWNLOAD_SECRET: na8nJcdXC5n312-SnsFO-Q
POLAR_S3_FILES_PRESIGN_TTL: "600"

# ============================================
# EMAIL (Resend ou outro provider)
# ============================================
POLAR_EMAIL_SENDER: resend
POLAR_RESEND_API_KEY: re_DWjnMKB5_8Hs6xJ6GF5DtCFgCWXTfGT4S
POLAR_EMAIL_FROM_NAME: Fluu
POLAR_EMAIL_FROM_DOMAIN: fluu.digital
POLAR_EMAIL_FROM_LOCAL: hello

# ============================================
# STRIPE (se estiver usando)
# ============================================
POLAR_STRIPE_SECRET_KEY: YOUR_STRIPE_SECRET_KEY
POLAR_STRIPE_WEBHOOK_SECRET: YOUR_STRIPE_WEBHOOK_SECRET

# ============================================
# GITHUB (se estiver usando)
# ============================================
POLAR_GITHUB_CLIENT_ID: YOUR_GITHUB_CLIENT_ID
POLAR_GITHUB_CLIENT_SECRET: YOUR_GITHUB_CLIENT_SECRET

# ============================================
# GOOGLE OAUTH (Login com Google)
# ============================================
# Configurado em: 2025-01-27
# Redirect URI configurado no Google Cloud: https://api.fluu.digital/v1/integrations/google/callback
# Documentação completa: _docs/CONFIGURAR_LOGIN_GOOGLE.md
POLAR_GOOGLE_CLIENT_ID: 923457232981-aj399ub5qflffahehklunc9sudu93eeu.apps.googleusercontent.com
POLAR_GOOGLE_CLIENT_SECRET: 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

# ============================================
# PERFORMANCE
# ============================================
POLAR_DATABASE_POOL_SIZE: "10"
POLAR_DATABASE_POOL_RECYCLE_SECONDS: "600"
POLAR_DATABASE_COMMAND_TIMEOUT_SECONDS: "30.0"

