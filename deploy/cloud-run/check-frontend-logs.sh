#!/bin/bash

# Script para verificar logs do frontend no Cloud Run

PROJECT_ID="pix-api-proxy-1758593444"
REGION="us-east1"
SERVICE_NAME="fluu-web"

echo "📋 Buscando logs do serviço ${SERVICE_NAME}..."
echo ""

# Buscar logs recentes
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME}" \
  --project=${PROJECT_ID} \
  --limit=50 \
  --format="table(timestamp,severity,textPayload)" \
  --freshness=1h

echo ""
echo "🔗 Para ver logs no console web:"
echo "https://console.cloud.google.com/logs/viewer?project=${PROJECT_ID}&resource=cloud_run_revision/service_name/${SERVICE_NAME}"

