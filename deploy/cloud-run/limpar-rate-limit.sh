#!/bin/bash
# Script para limpar rate limit do Redis (especialmente para OAuth endpoints)

set -e

REDIS_HOST="joint-hookworm-6489.upstash.io"
REDIS_PORT="6379"
REDIS_PASSWORD="ARlZAAImcDI5MzliMzdlMTQwZGQ0YWRjYWZlMWI4NWJkNDI1Y2RjNXAyNjQ4OQ"
REDIS_USERNAME="default"
REDIS_DB="0"
REDIS_URL="rediss://${REDIS_USERNAME}:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}"

echo "🧹 Limpando rate limit do Redis..."
echo ""

# Verificar se redis-cli está disponível
if ! command -v redis-cli &> /dev/null; then
    echo "❌ redis-cli não está instalado"
    echo "   Instale com: brew install redis (macOS) ou apt-get install redis-tools (Linux)"
    exit 1
fi

# Listar chaves de rate limit
echo "📊 Chaves de rate limit encontradas:"
KEYS=$(redis-cli -u "$REDIS_URL" KEYS "ratelimit:*" 2>/dev/null || echo "")
if [ -z "$KEYS" ]; then
    echo "   Nenhuma chave encontrada"
else
    echo "$KEYS" | while read -r key; do
        if [ -n "$key" ]; then
            echo "   - $key"
        fi
    done
fi

echo ""
echo "🗑️  Limpando todas as chaves de rate limit..."

# Limpar chaves de rate limit
if [ -n "$KEYS" ]; then
    echo "$KEYS" | while read -r key; do
        if [ -n "$key" ]; then
            redis-cli -u "$REDIS_URL" DEL "$key" 2>/dev/null || true
        fi
    done
    echo "✅ Rate limit limpo com sucesso!"
else
    echo "ℹ️  Nenhuma chave para limpar"
fi

echo ""
echo "📊 Verificando chaves restantes:"
REMAINING=$(redis-cli -u "$REDIS_URL" KEYS "ratelimit:*" 2>/dev/null | wc -l || echo "0")
echo "   Chaves restantes: $REMAINING"

