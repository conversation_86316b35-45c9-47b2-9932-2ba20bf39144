#!/bin/bash
# Script para diagnosticar problemas do worker

set -e

PROJECT_ID="pix-api-proxy-1758593444"
REGION="us-east1"
SERVICE_NAME="fluu-worker"

echo "🔍 Diagnóstico do Worker"
echo "========================"
echo ""

# 1. Verificar status do serviço
echo "1. Status do serviço:"
gcloud run services describe ${SERVICE_NAME} --region ${REGION} --format="get(status.conditions[0].status,status.conditions[0].type)" 2>/dev/null || echo "❌ Serviço não encontrado"
echo ""

# 2. Verificar variáveis de ambiente críticas
echo "2. Variáveis de ambiente críticas:"
gcloud run services describe ${SERVICE_NAME} --region ${REGION} \
  --format="get(spec.template.spec.containers[0].env)" | \
  grep -E "(POLAR_EMAIL_SENDER|POLAR_RESEND_API_KEY|POLAR_REDIS|DRAMATIQ_QUEUES)" || echo "❌ Variáveis não encontradas"
echo ""

# 3. Verificar logs recentes de erro
echo "3. Últimos erros (últimas 10 horas):"
gcloud logging read \
  "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME} AND severity>=ERROR" \
  --limit 10 \
  --format="table(timestamp,severity,textPayload)" \
  --project ${PROJECT_ID} \
  --freshness=10h || echo "Nenhum erro encontrado"
echo ""

# 4. Verificar logs de inicialização do Dramatiq
echo "4. Última inicialização do Dramatiq:"
gcloud logging read \
  "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME} AND textPayload=~\"Dramatiq.*booting\"" \
  --limit 1 \
  --format="table(timestamp,textPayload)" \
  --project ${PROJECT_ID} \
  --freshness=24h || echo "Nenhum log encontrado"
echo ""

# 5. Verificar se há tarefas sendo processadas
echo "5. Logs de processamento de tarefas (últimas 24h):"
gcloud logging read \
  "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME} AND (textPayload=~\"email\" OR textPayload=~\"resend\" OR textPayload=~\"TASK\")" \
  --limit 10 \
  --format="table(timestamp,severity,textPayload)" \
  --project ${PROJECT_ID} \
  --freshness=24h || echo "Nenhuma tarefa processada encontrada"
echo ""

# 6. Verificar conexão Redis (verificar logs de erro de conexão)
echo "6. Problemas de conexão Redis:"
gcloud logging read \
  "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME} AND (textPayload=~\"redis\" OR textPayload=~\"Redis\" OR textPayload=~\"connection\" OR textPayload=~\"Connection\")" \
  --limit 10 \
  --format="table(timestamp,severity,textPayload)" \
  --project ${PROJECT_ID} \
  --freshness=24h || echo "Nenhum problema de conexão encontrado"
echo ""

echo "✅ Diagnóstico completo!"
echo ""
echo "💡 Próximos passos:"
echo "   - Se não há tarefas sendo processadas, verifique se o backend está enfileirando emails"
echo "   - Se há erros de conexão Redis, verifique as credenciais"
echo "   - Se o worker não está iniciando, verifique os logs de inicialização"

