#!/bin/bash
set -e

# Configurações
PROJECT_ID="pix-api-proxy-1758593444"
REGION="us-east1"
SERVICE_NAME="fluu-web"
IMAGE_NAME="gcr.io/${PROJECT_ID}/polar-frontend"

echo "🚀 Iniciando deploy do frontend..."

# Verificar se estamos no diretório correto
if [ ! -f "clients/Dockerfile" ]; then
    echo "❌ Erro: Execute este script a partir da raiz do projeto"
    exit 1
fi

# Verificar se env-frontend.yaml existe
if [ ! -f "deploy/cloud-run/env-frontend.yaml" ]; then
    echo "❌ Erro: arquivo env-frontend.yaml não encontrado"
    exit 1
fi

# Extrair variáveis NEXT_PUBLIC_* do env-frontend.yaml para passar como build args
# O formato do arquivo é: VAR_NAME: value
echo "📋 Extraindo variáveis de ambiente para build..."
BUILD_ARGS=""

# Ler o arquivo env-frontend.yaml e extrair variáveis NEXT_PUBLIC_*
while IFS= read -r line || [ -n "$line" ]; do
    # Ignorar linhas vazias, comentários e seções
    if [[ -z "$line" ]] || [[ "$line" =~ ^[[:space:]]*# ]] || [[ "$line" =~ ^[[:space:]]*=== ]]; then
        continue
    fi
    
    # Extrair nome da variável e valor (formato: VAR_NAME: value)
    if [[ "$line" =~ ^[[:space:]]*([A-Z_]+):[[:space:]]*(.+)$ ]]; then
        VAR_NAME="${BASH_REMATCH[1]}"
        VAR_VALUE="${BASH_REMATCH[2]}"
        
        # Remover aspas do valor se existirem
        VAR_VALUE=$(echo "$VAR_VALUE" | sed -e 's/^"//' -e 's/"$//' -e "s/^'//" -e "s/'$//")
        
        # Adicionar apenas variáveis NEXT_PUBLIC_* como build args
        if [[ "$VAR_NAME" =~ ^NEXT_PUBLIC_ ]]; then
            BUILD_ARGS="${BUILD_ARGS} --build-arg ${VAR_NAME}=${VAR_VALUE}"
            echo "  ✓ ${VAR_NAME}=${VAR_VALUE}"
        fi
    fi
done < deploy/cloud-run/env-frontend.yaml

# Build da imagem com build args
echo "📦 Building frontend image com variáveis de ambiente..."
echo "⏱️  Iniciando build em $(date '+%Y-%m-%d %H:%M:%S')"
echo "📊 Mostrando progresso completo do build..."
echo ""
echo "💡 Dica: O build pode levar 10-20 minutos na primeira vez"
echo "💡 Dica: Use Ctrl+C para cancelar se necessário"
echo ""

# Verificar se o usuário quer usar cache (útil para builds subsequentes)
USE_CACHE="${USE_CACHE:-no}"
if [ "$USE_CACHE" = "yes" ]; then
    echo "📦 Usando cache do Docker (build mais rápido)"
    CACHE_FLAG=""
else
    echo "📦 Build limpo sem cache (garante build fresco)"
    CACHE_FLAG="--no-cache"
fi

# Usar --progress=plain para ver todos os logs do build
docker build \
  --progress=plain \
  ${CACHE_FLAG} \
  -f clients/Dockerfile \
  -t ${IMAGE_NAME}:latest \
  ${BUILD_ARGS} \
  . 2>&1 | tee /tmp/docker-build.log

BUILD_EXIT_CODE=${PIPESTATUS[0]}

if [ $BUILD_EXIT_CODE -ne 0 ]; then
    echo ""
    echo "❌ Erro no build da imagem (exit code: $BUILD_EXIT_CODE)"
    echo "📋 Últimas 50 linhas do log:"
    tail -n 50 /tmp/docker-build.log
    exit 1
fi

echo ""
echo "✅ Build concluído em $(date '+%Y-%m-%d %H:%M:%S')"

# Push da imagem
echo "⬆️  Pushing image to Google Container Registry..."
docker push ${IMAGE_NAME}:latest

if [ $? -ne 0 ]; then
    echo "❌ Erro no push da imagem"
    exit 1
fi

# Deploy no Cloud Run
echo "🚀 Deploying to Cloud Run..."
cd deploy/cloud-run

# Cloud Run injeta automaticamente a variável PORT
# Não especificamos --port para permitir que o Cloud Run gerencie a porta automaticamente
# O container deve escutar na porta definida pela variável PORT (geralmente 8080)
gcloud run deploy ${SERVICE_NAME} \
  --image ${IMAGE_NAME}:latest \
  --region ${REGION} \
  --platform managed \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --min-instances 0 \
  --max-instances 10 \
  --timeout 600 \
  --concurrency 80 \
  --env-vars-file=env-frontend.yaml \
  --port 8080 \
  --cpu-boost

if [ $? -ne 0 ]; then
    echo "❌ Erro no deploy"
    exit 1
fi

# Obter URL do serviço
FRONTEND_URL=$(gcloud run services describe ${SERVICE_NAME} \
  --region ${REGION} \
  --format="get(status.url)")

echo ""
echo "✅ Frontend deployado com sucesso!"
echo "📍 URL: ${FRONTEND_URL}"
echo ""

# Atualizar env-frontend.yaml com URL real
echo "📝 Atualizando env-frontend.yaml com URL do frontend..."
sed -i.bak "s|NEXT_PUBLIC_FRONTEND_BASE_URL:.*|NEXT_PUBLIC_FRONTEND_BASE_URL: ${FRONTEND_URL}|" env-frontend.yaml

# Redesploy com URL atualizada
echo "🔄 Redesployando com URL atualizada..."
gcloud run services update ${SERVICE_NAME} \
  --region ${REGION} \
  --env-vars-file=env-frontend.yaml

echo ""
echo "✅ Frontend URL atualizada em env-frontend.yaml"
echo "🧪 Teste o frontend:"
echo "   curl -I ${FRONTEND_URL}"
echo ""
echo "⚠️  IMPORTANTE: Atualize o CORS no backend para incluir:"
echo "   ${FRONTEND_URL}"
echo ""

