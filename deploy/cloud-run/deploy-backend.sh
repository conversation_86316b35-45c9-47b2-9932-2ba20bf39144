#!/bin/bash
set -e

# Configurações
PROJECT_ID="pix-api-proxy-1758593444"
REGION="us-east1"
SERVICE_NAME="fluu-api"
IMAGE_NAME="gcr.io/${PROJECT_ID}/polar-api"

echo "🚀 Iniciando deploy do backend..."

# Verificar se estamos no diretório correto
if [ ! -f "server/Dockerfile" ]; then
    echo "❌ Erro: Execute este script a partir da raiz do projeto"
    exit 1
fi

# Build da imagem
echo "📦 Building backend image..."
cd server
docker build -t ${IMAGE_NAME}:latest -f Dockerfile .

if [ $? -ne 0 ]; then
    echo "❌ Erro no build da imagem"
    exit 1
fi

# Push da imagem
echo "⬆️  Pushing image to Google Container Registry..."
docker push ${IMAGE_NAME}:latest

if [ $? -ne 0 ]; then
    echo "❌ Erro no push da imagem"
    exit 1
fi

# Deploy no Cloud Run
echo "🚀 Deploying to Cloud Run..."
cd ../deploy/cloud-run

if [ ! -f "env.yaml" ]; then
    echo "❌ Erro: arquivo env.yaml não encontrado"
    exit 1
fi

gcloud run deploy ${SERVICE_NAME} \
  --image ${IMAGE_NAME}:latest \
  --region ${REGION} \
  --platform managed \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --min-instances 1 \
  --max-instances 10 \
  --timeout 300 \
  --concurrency 80 \
  --env-vars-file=env.yaml

if [ $? -ne 0 ]; then
    echo "❌ Erro no deploy"
    exit 1
fi

# Obter URL do serviço
BACKEND_URL=$(gcloud run services describe ${SERVICE_NAME} \
  --region ${REGION} \
  --format="get(status.url)")

echo ""
echo "✅ Backend deployado com sucesso!"
echo "📍 URL: ${BACKEND_URL}"
echo ""
echo "🧪 Teste o health check:"
echo "   curl ${BACKEND_URL}/healthz"
echo ""

