#!/bin/bash
# Script completo para aplicar migrations e criar usuário admin

set -e

cd "$(dirname "$0")/../../server"

# Carregar variáveis de ambiente
export POLAR_ENV=production
export POLAR_POSTGRES_USER=neondb_owner
export POLAR_POSTGRES_PWD=npg_iX2kVBloh1YT
export POLAR_POSTGRES_HOST=ep-dry-cell-achqzu4j-pooler.sa-east-1.aws.neon.tech
export POLAR_POSTGRES_PORT=5432
export POLAR_POSTGRES_DATABASE=neondb
export POLAR_POSTGRES_SSLMODE=require

echo "🔄 Aplicando migrations..."
uv run python -m scripts.db upgrade

echo ""
echo "✅ Migrations aplicadas!"
echo ""
echo "🔧 Criando usuário admin..."
uv run python scripts/migrate_and_create_admin.py

