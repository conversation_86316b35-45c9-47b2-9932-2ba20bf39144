# Fix: Erro de Build do Frontend

## 🔴 Problema

O frontend está dando erro:
```
Error: Could not find a production build in the '.next' directory.
```

## 🔍 Causa

O build do Next.js não está sendo gerado corretamente durante o build da imagem Docker. Possíveis causas:

1. **Build falhando silenciosamente**: O comando `pnpm run build --filter=web` pode estar falhando sem erro visível
2. **Variáveis de ambiente faltando**: Variáveis `NEXT_PUBLIC_*` podem não estar sendo passadas corretamente
3. **Diretório .next não sendo copiado**: O diretório pode estar sendo gerado mas não copiado para o stage final

## ✅ Solução Aplicada

### 1. Validações no Workflow

Adicionado no `.github/workflows/build-and-push.yml`:

- ✅ Validação de variáveis de build antes do build
- ✅ Verificação de sucesso do build Docker
- ✅ Verificação se o diretório `.next` foi gerado na imagem
- ✅ Logs detalhados para debug

### 2. Próximos Passos

1. **Verificar logs do build** no GitHub Actions
2. **Verificar se variáveis NEXT_PUBLIC_* estão configuradas** em `env-frontend.yaml`
3. **Rebuild da imagem** via GitHub Actions
4. **Redeploy do frontend**

## 🚀 Como Resolver

### Opção 1: Rebuild via GitHub Actions

1. Vá para GitHub Actions
2. Execute "Build and Push to GCR" manualmente
3. Verifique os logs do build do frontend
4. Se build for bem-sucedido, execute "Deploy Frontend to Cloud Run"

### Opção 2: Build Local para Testar

```bash
# Extrair variáveis de build
cd deploy/cloud-run
BUILD_ARGS=""
while IFS= read -r line; do
  if [[ "$line" =~ ^[[:space:]]*([A-Z_]+):[[:space:]]*(.+)$ ]]; then
    VAR_NAME="${BASH_REMATCH[1]}"
    VAR_VALUE="${BASH_REMATCH[2]}"
    if [[ "$VAR_NAME" =~ ^NEXT_PUBLIC_ ]]; then
      BUILD_ARGS="$BUILD_ARGS --build-arg ${VAR_NAME}=${VAR_VALUE}"
    fi
  fi
done < env-frontend.yaml

# Build local
cd ../..
docker build --progress=plain -f clients/Dockerfile $BUILD_ARGS -t test-frontend .

# Verificar se .next foi gerado
docker run --rm --entrypoint sh test-frontend -c "ls -la /app/clients/apps/web/.next"
```

## 📋 Checklist de Debug

- [ ] Verificar se `env-frontend.yaml` tem variáveis `NEXT_PUBLIC_*`
- [ ] Verificar logs do build no GitHub Actions
- [ ] Verificar se o build do Next.js está sendo executado (linha 109 do Dockerfile)
- [ ] Verificar se o diretório `.next` está sendo copiado (linha 154 do Dockerfile)
- [ ] Testar build local para identificar o problema

## 🔧 Comandos Úteis

```bash
# Ver logs do frontend no Cloud Run
gcloud run services logs read fluu-web --region us-east1 --limit 50

# Ver logs de erro
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=fluu-web AND severity>=ERROR" --limit 20 --project pix-api-proxy-1758593444

# Verificar última imagem buildada
gcloud container images list-tags gcr.io/pix-api-proxy-1758593444/polar-frontend --limit 1
```

---

**Data**: 2025-11-08
**Status**: ⏳ Aguardando rebuild para validar correções

