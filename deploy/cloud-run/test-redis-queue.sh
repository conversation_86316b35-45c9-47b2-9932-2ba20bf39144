#!/bin/bash
# Script para testar conexão Redis e verificar tarefas na fila

set -e

echo "🔍 Testando conexão Redis e filas"
echo "=================================="
echo ""

# Variáveis do Redis (do env-worker.yaml)
REDIS_HOST="joint-hookworm-6489.upstash.io"
REDIS_PORT="6379"
REDIS_PASSWORD="ARlZAAImcDI5MzliMzdlMTQwZGQ0YWRjYWZlMWI4NWJkNDI1Y2RjNXAyNjQ4OQ"
REDIS_USERNAME="default"
REDIS_DB="0"

echo "1. Testando conexão Redis..."
echo "   Host: $REDIS_HOST"
echo "   Port: $REDIS_PORT"
echo ""

# Verificar se redis-cli está disponível
if command -v redis-cli &> /dev/null; then
    echo "2. Verificando filas do Dramatiq..."
    echo ""
    
    # Construir comando redis-cli com SSL
    REDIS_URL="rediss://${REDIS_USERNAME}:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}"
    
    echo "   Filas configuradas: high_priority, default"
    echo ""
    
    # Verificar tamanho das filas
    echo "   Tamanho da fila 'high_priority':"
    redis-cli -u "$REDIS_URL" LLEN "dramatiq:high_priority" 2>/dev/null || echo "   ❌ Erro ao conectar ou fila não existe"
    echo ""
    
    echo "   Tamanho da fila 'default':"
    redis-cli -u "$REDIS_URL" LLEN "dramatiq:default" 2>/dev/null || echo "   ❌ Erro ao conectar ou fila não existe"
    echo ""
    
    echo "   Mensagens na fila 'high_priority' (primeiras 5):"
    redis-cli -u "$REDIS_URL" LRANGE "dramatiq:high_priority" 0 4 2>/dev/null || echo "   ❌ Erro ao ler fila"
    echo ""
    
    echo "   Mensagens na fila 'default' (primeiras 5):"
    redis-cli -u "$REDIS_URL" LRANGE "dramatiq:default" 0 4 2>/dev/null || echo "   ❌ Erro ao ler fila"
    echo ""
    
    echo "   Chaves relacionadas ao Dramatiq:"
    redis-cli -u "$REDIS_URL" KEYS "dramatiq:*" 2>/dev/null | head -20 || echo "   ❌ Erro ao listar chaves"
    echo ""
else
    echo "⚠️  redis-cli não está instalado. Instalando dependências..."
    echo ""
    echo "   Para testar manualmente, você pode:"
    echo "   1. Instalar redis-cli: brew install redis (macOS) ou apt-get install redis-tools (Linux)"
    echo "   2. Conectar manualmente:"
    echo "      redis-cli -u rediss://${REDIS_USERNAME}:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}"
    echo "   3. Verificar filas:"
    echo "      LLEN dramatiq:high_priority"
    echo "      LLEN dramatiq:default"
    echo "      LRANGE dramatiq:high_priority 0 10"
    echo ""
fi

echo "✅ Teste completo!"
echo ""
echo "💡 Interpretação:"
echo "   - Se as filas têm tamanho > 0, há tarefas aguardando processamento"
echo "   - Se as filas estão vazias, não há tarefas ou já foram processadas"
echo "   - Se há erro de conexão, verifique as credenciais do Redis"

