-- Script SQL para criar usuário admin diretamente
-- Execute após aplicar migrations

-- Verificar se usuário existe
DO $$
DECLARE
    user_id UUID;
    user_exists BOOLEAN;
BEGIN
    -- Verificar se usuário existe
    SELECT EXISTS(SELECT 1 FROM users WHERE LOWER(email) = LOWER('<EMAIL>')) INTO user_exists;
    
    IF user_exists THEN
        -- Atualizar usuário existente para admin
        UPDATE users
        SET is_admin = TRUE,
            deleted_at = NULL,
            blocked_at = NULL,
            updated_at = NOW()
        WHERE LOWER(email) = LOWER('<EMAIL>')
        RETURNING id INTO user_id;
        
        RAISE NOTICE 'Usuário atualizado para admin. ID: %', user_id;
    ELSE
        -- Criar novo usuário admin
        user_id := gen_random_uuid();
        
        INSERT INTO users (
            id, email, is_admin, email_verified,
            accepted_terms_of_service, identity_verification_status,
            created_at, updated_at, meta
        )
        VALUES (
            user_id,
            '<EMAIL>',
            TRUE,
            FALSE,
            FALSE,
            'unverified',
            NOW(),
            NOW(),
            '{}'::jsonb
        );
        
        RAISE NOTICE 'Usuário admin criado. ID: %', user_id;
    END IF;
    
    -- Verificar resultado
    PERFORM 1 FROM users WHERE id = user_id AND is_admin = TRUE AND deleted_at IS NULL AND blocked_at IS NULL;
    
    IF FOUND THEN
        RAISE NOTICE 'SUCESSO! Usuário admin criado/atualizado com sucesso!';
    ELSE
        RAISE EXCEPTION 'ERRO: Falha na verificação do usuário';
    END IF;
END $$;

-- Verificar resultado final
SELECT id, email, is_admin, deleted_at IS NULL as not_deleted, blocked_at IS NULL as not_blocked
FROM users
WHERE LOWER(email) = LOWER('<EMAIL>');

