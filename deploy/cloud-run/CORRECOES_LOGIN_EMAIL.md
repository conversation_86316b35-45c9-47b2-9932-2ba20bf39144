# Correções: Login e Email

## 🔴 Problemas Identificados

### 1. Erro CSP bloqueando login
**Erro**: `form-action 'self' https://api.fluu.digital` bloqueia o envio do formulário

**Causa**: O CSP está usando `${apiUrl}` que pode ter espaços ou formatação incorreta

**Solução**: ✅ Corrigido - Extrair apenas o domínio e usar `https://${apiDomain}`

### 2. Fila do Redis com tarefas acumuladas
**Status**: 13 tarefas na fila `default`, 0 na `high_priority`

**Causa**: Worker não está processando ou processando muito devagar

**Solução**: 
- Limpar fila antiga
- Verificar se worker está processando
- Monitorar processamento

### 3. Login com código não funciona
**Problema**: Formulário não aceita código ou não envia corretamente

**Solução**: Verificar se CSP está permitindo o form-action

### 4. OAuth Discord/Google não configurado
**Problema**: Variáveis de ambiente não configuradas

**Solução**: Adicionar variáveis necessárias

---

## ✅ Correções Aplicadas

### 1. CSP Corrigido (`next.config.mjs`)

```javascript
// Antes:
form-action 'self' ${apiUrl};

// Depois:
const apiDomain = apiUrlForCSP.replace(/^https?:\/\//, '').split('/')[0]
form-action 'self' https://${apiDomain};
```

Isso garante que o CSP permita form submissions para `https://api.fluu.digital`

### 2. Script para Limpar Fila Redis

Criado `deploy/cloud-run/limpar-fila-redis.sh` para limpar tarefas antigas.

---

## 🚀 Próximos Passos

### 1. Limpar Fila do Redis

```bash
cd deploy/cloud-run
./limpar-fila-redis.sh
```

Ou manualmente:
```bash
redis-cli -u "rediss://default:PASSWORD@HOST:6379/0" DEL "dramatiq:default"
redis-cli -u "rediss://default:PASSWORD@HOST:6379/0" DEL "dramatiq:default.msgs"
```

### 2. Verificar Worker

```bash
# Verificar se está processando
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=fluu-worker AND textPayload=~\"email\"" --limit 10 --project pix-api-proxy-1758593444 --freshness=10m

# Verificar tamanho das filas após limpeza
redis-cli -u "rediss://..." LLEN "dramatiq:high_priority"
redis-cli -u "rediss://..." LLEN "dramatiq:default"
```

### 3. Configurar OAuth Discord/Google

**Variáveis necessárias no backend (`env.yaml`):**

```yaml
# Discord
POLAR_DISCORD_CLIENT_ID: YOUR_DISCORD_CLIENT_ID
POLAR_DISCORD_CLIENT_SECRET: YOUR_DISCORD_CLIENT_SECRET

# Google
POLAR_GOOGLE_CLIENT_ID: YOUR_GOOGLE_CLIENT_ID
POLAR_GOOGLE_CLIENT_SECRET: YOUR_GOOGLE_CLIENT_SECRET
```

**Como obter credenciais:**

1. **Discord:**
   - Acesse: https://discord.com/developers/applications
   - Crie uma aplicação
   - Vá em "OAuth2"
   - Adicione redirect URI: `https://api.fluu.digital/v1/integrations/discord/callback`
   - Copie Client ID e Client Secret

2. **Google:**
   - Acesse: https://console.cloud.google.com/apis/credentials
   - Crie OAuth 2.0 Client ID
   - Adicione redirect URI: `https://api.fluu.digital/v1/integrations/google/callback`
   - Copie Client ID e Client Secret

### 4. Redeploy do Frontend

Após corrigir o CSP, fazer rebuild e redeploy:
```bash
# Via GitHub Actions ou manualmente
./deploy/cloud-run/deploy-frontend.sh
```

---

## 📋 Checklist de Verificação

- [ ] CSP corrigido no `next.config.mjs`
- [ ] Fila do Redis limpa
- [ ] Worker processando tarefas
- [ ] Login com código funcionando
- [ ] OAuth Discord configurado (se necessário)
- [ ] OAuth Google configurado (se necessário)
- [ ] Frontend redeployado com correções

---

## 🔧 Comandos Úteis

```bash
# Limpar fila
./deploy/cloud-run/limpar-fila-redis.sh

# Verificar worker
gcloud run services logs read fluu-worker --region us-east1 --limit 50

# Verificar tamanho das filas
redis-cli -u "rediss://..." LLEN "dramatiq:high_priority"
redis-cli -u "rediss://..." LLEN "dramatiq:default"

# Testar login
# Acesse https://fluu.digital/login e tente fazer login
```

---

**Data**: 2025-11-08

