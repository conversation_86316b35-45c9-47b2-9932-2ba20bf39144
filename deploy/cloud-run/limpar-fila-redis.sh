#!/bin/bash
# Script para limpar a fila do Redis

set -e

REDIS_HOST="joint-hookworm-6489.upstash.io"
REDIS_PORT="6379"
REDIS_PASSWORD="ARlZAAImcDI5MzliMzdlMTQwZGQ0YWRjYWZlMWI4NWJkNDI1Y2RjNXAyNjQ4OQ"
REDIS_USERNAME="default"
REDIS_DB="0"
REDIS_URL="rediss://${REDIS_USERNAME}:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}"

echo "🧹 Limpando filas do Redis..."
echo ""

# Verificar tamanho atual
echo "📊 Tamanho atual das filas:"
HIGH_PRIORITY_SIZE=$(redis-cli -u "$REDIS_URL" LLEN "dramatiq:high_priority" 2>/dev/null || echo "0")
DEFAULT_SIZE=$(redis-cli -u "$REDIS_URL" LLEN "dramatiq:default" 2>/dev/null || echo "0")

echo "   high_priority: $HIGH_PRIORITY_SIZE tarefas"
echo "   default: $DEFAULT_SIZE tarefas"
echo ""

# Confirmar antes de limpar
read -p "⚠️  Tem certeza que deseja limpar TODAS as tarefas? (sim/não): " confirm
if [ "$confirm" != "sim" ]; then
    echo "❌ Operação cancelada"
    exit 0
fi

# Limpar filas
echo ""
echo "🗑️  Limpando fila high_priority..."
redis-cli -u "$REDIS_URL" DEL "dramatiq:high_priority" 2>/dev/null || echo "   (já estava vazia)"
redis-cli -u "$REDIS_URL" DEL "dramatiq:high_priority.msgs" 2>/dev/null || true
redis-cli -u "$REDIS_URL" DEL "dramatiq:high_priority.DQ.msgs" 2>/dev/null || true

echo "🗑️  Limpando fila default..."
redis-cli -u "$REDIS_URL" DEL "dramatiq:default" 2>/dev/null || echo "   (já estava vazia)"
redis-cli -u "$REDIS_URL" DEL "dramatiq:default.msgs" 2>/dev/null || true
redis-cli -u "$REDIS_URL" DEL "dramatiq:default.DQ.msgs" 2>/dev/null || true

# Limpar mensagens órfãs (opcional)
echo ""
echo "🧹 Limpando mensagens órfãs..."
redis-cli -u "$REDIS_URL" KEYS "dramatiq:__acks__.*" 2>/dev/null | xargs -r redis-cli -u "$REDIS_URL" DEL 2>/dev/null || true

echo ""
echo "✅ Filas limpas com sucesso!"
echo ""
echo "📊 Tamanho após limpeza:"
redis-cli -u "$REDIS_URL" LLEN "dramatiq:high_priority" 2>/dev/null || echo "   0"
redis-cli -u "$REDIS_URL" LLEN "dramatiq:default" 2>/dev/null || echo "   0"

