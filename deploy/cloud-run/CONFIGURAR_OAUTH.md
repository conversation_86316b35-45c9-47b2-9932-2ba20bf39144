# Configuração OAuth: Discord e Google

## 📋 O que é necessário

Para habilitar login com Discord e Google, você precisa:

1. **Criar aplicações OAuth** nos respectivos provedores
2. **Configurar variáveis de ambiente** no backend
3. **Adicionar redirect URIs** corretos

---

## 🔵 Discord OAuth

### 1. Criar Aplicação Discord

1. Acesse: https://discord.com/developers/applications
2. Clique em "New Application"
3. Dê um nome (ex: "Fluu")
4. Vá em "OAuth2" → "General"
5. Copie o **Client ID** e **Client Secret**

### 2. Configurar Redirect URI

Na seção "OAuth2" → "Redirects", adicione:
```
https://api.fluu.digital/v1/integrations/discord/callback
```

### 3. Configurar Variáveis de Ambiente

Adicione em `deploy/cloud-run/env.yaml`:

```yaml
# Discord OAuth
POLAR_DISCORD_CLIENT_ID: seu_client_id_aqui
POLAR_DISCORD_CLIENT_SECRET: seu_client_secret_aqui
```

### 4. Scopes Necessários

O código já está configurado para usar:
- `identify`
- `email`
- `guilds.join`

---

## 🔴 Google OAuth

### 1. Criar Projeto no Google Cloud

1. Acesse: https://console.cloud.google.com/
2. Crie um novo projeto ou selecione existente
3. Vá em "APIs & Services" → "Credentials"
4. Clique em "Create Credentials" → "OAuth client ID"
5. Se solicitado, configure a tela de consentimento OAuth
6. Tipo: "Web application"
7. Adicione **Authorized redirect URIs**:
   ```
   https://api.fluu.digital/v1/integrations/google/callback
   ```
8. Copie o **Client ID** e **Client Secret**

### 2. Configurar Variáveis de Ambiente

Adicione em `deploy/cloud-run/env.yaml`:

```yaml
# Google OAuth
POLAR_GOOGLE_CLIENT_ID: seu_client_id_aqui
POLAR_GOOGLE_CLIENT_SECRET: seu_client_secret_aqui
```

### 3. Scopes Necessários

O código já está configurado para usar:
- `https://www.googleapis.com/auth/userinfo.profile`
- `https://www.googleapis.com/auth/userinfo.email`

---

## ✅ Verificação

Após configurar:

1. **Redeploy do backend** com as novas variáveis:
   ```bash
   ./deploy/cloud-run/deploy-backend.sh
   ```

2. **Testar login**:
   - Acesse https://fluu.digital/login
   - Clique em "Continue with Google" ou "Continue with Discord"
   - Deve redirecionar para o provedor OAuth

---

## 🔧 Endpoints OAuth

### Discord
- **Authorize**: `/v1/integrations/discord/authorize`
- **Callback**: `/v1/integrations/discord/callback`

### Google
- **Authorize**: `/v1/integrations/google/authorize`
- **Callback**: `/v1/integrations/google/callback`

---

## 📝 Notas Importantes

1. **Redirect URIs devem ser HTTPS** (exceto localhost)
2. **Client Secrets são sensíveis** - não commitar no git
3. **Testar em produção** após configurar
4. **Verificar logs** se houver problemas

---

**Data**: 2025-11-08

