#!/bin/bash
# Script de deploy para Google Cloud Run
# Uso: ./deploy.sh [SERVICE_NAME] [REGION] [PROJECT_ID]

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configurações
SERVICE_NAME="${1:-fluu-api}"
REGION="${2:-us-east1}"
PROJECT_ID="${3:-}"
IMAGE_NAME="gcr.io/${PROJECT_ID}/polar-api"
DOCKERFILE_PATH="../server/Dockerfile"

# Verificar se o gcloud está instalado
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}Erro: gcloud CLI não está instalado.${NC}"
    echo "Instale em: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Verificar se o Docker está instalado
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Erro: Docker não está instalado.${NC}"
    exit 1
fi

# Verificar se PROJECT_ID foi fornecido
if [ -z "$PROJECT_ID" ]; then
    echo -e "${YELLOW}PROJECT_ID não fornecido, tentando obter do gcloud config...${NC}"
    PROJECT_ID=$(gcloud config get-value project 2>/dev/null)
    
    if [ -z "$PROJECT_ID" ]; then
        echo -e "${RED}Erro: PROJECT_ID não encontrado.${NC}"
        echo "Use: ./deploy.sh [SERVICE_NAME] [REGION] [PROJECT_ID]"
        exit 1
    fi
    echo -e "${GREEN}Usando PROJECT_ID: ${PROJECT_ID}${NC}"
fi

echo -e "${GREEN}=== Deploy do Polar para Google Cloud Run ===${NC}"
echo "Service: ${SERVICE_NAME}"
echo "Region: ${REGION}"
echo "Project: ${PROJECT_ID}"
echo "Image: ${IMAGE_NAME}"
echo ""

# Verificar autenticação
echo -e "${YELLOW}Verificando autenticação...${NC}"
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${YELLOW}Autenticando no Google Cloud...${NC}"
    gcloud auth login
fi

# Configurar projeto
echo -e "${YELLOW}Configurando projeto...${NC}"
gcloud config set project ${PROJECT_ID}

# Habilitar APIs necessárias
echo -e "${YELLOW}Habilitando APIs necessárias...${NC}"
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Build da imagem Docker
echo -e "${YELLOW}Construindo imagem Docker...${NC}"
cd "$(dirname "$0")/../.."
docker build -t ${IMAGE_NAME} -f ${DOCKERFILE_PATH} ./server

# Push da imagem para Google Container Registry
echo -e "${YELLOW}Enviando imagem para Google Container Registry...${NC}"
docker push ${IMAGE_NAME}

# Deploy no Cloud Run
echo -e "${YELLOW}Fazendo deploy no Cloud Run...${NC}"
gcloud run deploy ${SERVICE_NAME} \
    --image ${IMAGE_NAME} \
    --region ${REGION} \
    --platform managed \
    --allow-unauthenticated \
    --port 10000 \
    --memory 2Gi \
    --cpu 2 \
    --min-instances 1 \
    --max-instances 10 \
    --timeout 300 \
    --concurrency 80 \
    --set-env-vars-file=../cloud-run/env.yaml

echo -e "${GREEN}=== Deploy concluído com sucesso! ===${NC}"
echo ""
echo "URL do serviço:"
gcloud run services describe ${SERVICE_NAME} --region ${REGION} --format 'value(status.url)'

