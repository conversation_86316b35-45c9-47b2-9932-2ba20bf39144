# Guia Completo: Resolução de Problemas do Worker de Email

## 📋 Índice
1. [Diagnóstic<PERSON> Rápido](#diagnóstico-rápido)
2. [Resolução Passo a Passo](#resolução-passo-a-passo)
3. [Prevenção](#prevenção)
4. [Checklist de Verificação](#checklist-de-verificação)

---

## 🔍 Diagnóstico Rápido

### Sintomas
- Emails não são enviados
- Worker está rodando mas não processa tarefas
- Tarefas acumulam nas filas do Redis

### Comandos de Diagnóstico

```bash
# 1. Verificar status do worker
gcloud run services describe fluu-worker --region us-east1 --format="get(status.conditions[0].status)"

# 2. Verificar tamanho das filas
redis-cli -u "rediss://default:PASSWORD@HOST:6379/0" LLEN "dramatiq:high_priority"
redis-cli -u "rediss://default:PASSWORD@HOST:6379/0" LLEN "dramatiq:default"

# 3. Verificar logs do worker
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=fluu-worker" --limit 20 --project pix-api-proxy-1758593444

# 4. Verificar se há tarefas sendo processadas
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=fluu-worker AND textPayload=~\"email\"" --limit 10 --project pix-api-proxy-1758593444
```

---

## 🛠️ Resolução Passo a Passo

### Passo 1: Verificar Configuração das Filas

**Problema comum**: Dramatiq espera filas separadas por **espaço**, mas a variável pode estar com **vírgula**.

```bash
# Verificar variável de ambiente
gcloud run services describe fluu-worker --region us-east1 \
  --format="get(spec.template.spec.containers[0].env)" | grep DRAMATIQ_QUEUES
```

**Solução**: O script `start-worker.sh` deve converter vírgulas em espaços:

```bash
# Script correto (já implementado)
QUEUES=$(echo ${DRAMATIQ_QUEUES:-high_priority,default} | tr ',' ' ')
exec uv run dramatiq --queues ${QUEUES} ...
```

### Passo 2: Verificar se a Imagem Docker Está Atualizada

**Problema comum**: A imagem Docker pode não conter as correções mais recentes.

```bash
# Verificar data da última build
gcloud container images list-tags gcr.io/pix-api-proxy-1758593444/polar-api \
  --limit 1 --format="get(timestamp)" --project pix-api-proxy-1758593444

# Comparar com a data da última correção no código
git log -1 --format="%ai" server/start-worker.sh
```

**Solução**: Reconstruir a imagem se necessário:

```bash
cd server
docker build -t gcr.io/pix-api-proxy-1758593444/polar-api:latest -f Dockerfile .
docker push gcr.io/pix-api-proxy-1758593444/polar-api:latest
```

### Passo 3: Verificar Conexão Redis

```bash
# Testar conexão
redis-cli -u "rediss://default:PASSWORD@HOST:6379/0" PING

# Verificar se há tarefas na fila
redis-cli -u "rediss://default:PASSWORD@HOST:6379/0" LLEN "dramatiq:high_priority"
redis-cli -u "rediss://default:PASSWORD@HOST:6379/0" LRANGE "dramatiq:high_priority" 0 4
```

### Passo 4: Verificar Variáveis de Email

```bash
# Verificar configuração de email
gcloud run services describe fluu-worker --region us-east1 \
  --format="get(spec.template.spec.containers[0].env)" | grep -E "POLAR_EMAIL|POLAR_RESEND"
```

**Variáveis necessárias**:
- `POLAR_EMAIL_SENDER=resend`
- `POLAR_RESEND_API_KEY` (deve estar configurado)
- `POLAR_EMAIL_FROM_NAME`
- `POLAR_EMAIL_FROM_DOMAIN`
- `POLAR_EMAIL_FROM_LOCAL`

### Passo 5: Redeploy do Worker

```bash
# Redeploy com configuração atualizada
cd deploy/cloud-run
./deploy-worker.sh
```

### Passo 6: Monitorar Processamento

```bash
# Monitorar logs em tempo real
gcloud run services logs read fluu-worker --region us-east1 --limit 50

# Verificar se tarefas estão sendo processadas
watch -n 5 'redis-cli -u "rediss://default:PASSWORD@HOST:6379/0" LLEN "dramatiq:high_priority"'
```

---

## 🛡️ Prevenção

### 1. Validação no Script de Deploy

Adicionar validação no `deploy-worker.sh`:

```bash
# Validar formato das filas antes do deploy
if [[ "$DRAMATIQ_QUEUES" == *","* ]]; then
  echo "⚠️  AVISO: DRAMATIQ_QUEUES contém vírgulas. Será convertido para espaços."
fi
```

### 2. Testes Automatizados

Criar teste que verifica:
- Worker consegue conectar ao Redis
- Worker está escutando as filas corretas
- Tarefas são processadas corretamente

### 3. Monitoramento

Configurar alertas para:
- Tamanho das filas > 100 tarefas
- Worker não processa tarefas por > 5 minutos
- Erros de conexão Redis

### 4. Documentação

Manter documentação atualizada sobre:
- Formato correto das variáveis de ambiente
- Processo de deploy
- Troubleshooting comum

---

## ✅ Checklist de Verificação

Use este checklist sempre que o worker não estiver processando emails:

- [ ] Worker está rodando (status `Ready`)
- [ ] Variável `DRAMATIQ_QUEUES` está configurada
- [ ] Script `start-worker.sh` converte vírgulas em espaços
- [ ] Imagem Docker está atualizada (contém correções recentes)
- [ ] Conexão Redis está funcionando
- [ ] Variáveis de email estão configuradas (`POLAR_EMAIL_SENDER`, `POLAR_RESEND_API_KEY`)
- [ ] Worker está escutando as filas corretas (verificar logs)
- [ ] Não há erros nos logs do worker
- [ ] Tarefas estão sendo enfileiradas pelo backend
- [ ] Worker processa tarefas (tamanho das filas diminui)

---

## 🔧 Scripts Úteis

### Script de Diagnóstico Completo

```bash
#!/bin/bash
# deploy/cloud-run/diagnose-worker.sh

PROJECT_ID="pix-api-proxy-1758593444"
REGION="us-east1"
SERVICE_NAME="fluu-worker"

echo "🔍 Diagnóstico do Worker"
echo "========================"

# Status do serviço
echo "1. Status:"
gcloud run services describe ${SERVICE_NAME} --region ${REGION} \
  --format="get(status.conditions[0].status)" 2>/dev/null

# Tamanho das filas
echo "2. Tamanho das filas:"
redis-cli -u "rediss://..." LLEN "dramatiq:high_priority"
redis-cli -u "rediss://..." LLEN "dramatiq:default"

# Últimos logs
echo "3. Últimos logs:"
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME}" \
  --limit 5 --project ${PROJECT_ID} --freshness=10m
```

### Script de Teste de Processamento

```bash
#!/bin/bash
# deploy/cloud-run/test-email-processing.sh

# Enfileirar uma tarefa de teste
# Verificar se é processada em < 30 segundos
```

---

## 📚 Referências

- [Dramatiq Documentation](https://dramatiq.io/)
- [Google Cloud Run Logs](https://cloud.google.com/run/docs/logging)
- [Redis CLI Documentation](https://redis.io/docs/manual/cli/)

---

## 🆘 Suporte

Se o problema persistir após seguir este guia:

1. Execute o script de diagnóstico completo
2. Colete logs dos últimos 30 minutos
3. Verifique se há mudanças recentes no código
4. Verifique se há problemas conhecidos no status do Redis/Upstash

