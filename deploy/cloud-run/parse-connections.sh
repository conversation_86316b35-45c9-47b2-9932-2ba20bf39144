#!/bin/bash
# Script para parsear URLs de conexão e gerar variáveis de ambiente
# Uso: ./parse-connections.sh

set -e

# Cores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${GREEN}=== Parseador de URLs de Conexão ===${NC}"
echo ""

# Redis URL (Upstash)
read -p "Cole a URL do Redis (Upstash): " REDIS_URL
echo ""

# PostgreSQL URL (Neon)
read -p "Cole a URL do PostgreSQL (Neon): " POSTGRES_URL
echo ""

echo -e "${YELLOW}=== Variáveis de Ambiente Extraídas ===${NC}"
echo ""

# Parse Redis
if [[ $REDIS_URL == redis://* ]]; then
    # Remove redis://
    REDIS_URL=${REDIS_URL#redis://}
    
    # Extrair username:password@host:port
    if [[ $REDIS_URL == *@* ]]; then
        # Tem autenticação
        AUTH_PART=${REDIS_URL%%@*}
        HOST_PORT=${REDIS_URL#*@}
        
        if [[ $AUTH_PART == *:* ]]; then
            REDIS_USERNAME=${AUTH_PART%%:*}
            REDIS_PASSWORD=${AUTH_PART#*:}
        else
            REDIS_USERNAME="default"
            REDIS_PASSWORD=$AUTH_PART
        fi
    else
        REDIS_USERNAME="default"
        REDIS_PASSWORD=""
        HOST_PORT=$REDIS_URL
    fi
    
    if [[ $HOST_PORT == *:* ]]; then
        REDIS_HOST=${HOST_PORT%%:*}
        REDIS_PORT=${HOST_PORT#*:}
    else
        REDIS_HOST=$HOST_PORT
        REDIS_PORT="6379"
    fi
    
    echo "# Redis (Upstash)"
    echo "POLAR_REDIS_HOST: ${REDIS_HOST}"
    echo "POLAR_REDIS_PORT: \"${REDIS_PORT}\""
    echo "POLAR_REDIS_DB: \"0\""
    echo "POLAR_REDIS_PASSWORD: ${REDIS_PASSWORD}"
    echo "POLAR_REDIS_USERNAME: ${REDIS_USERNAME}"
    echo "POLAR_REDIS_SSL: \"true\""
    echo ""
fi

# Parse PostgreSQL
if [[ $POSTGRES_URL == postgresql://* ]] || [[ $POSTGRES_URL == postgres://* ]]; then
    # Remove postgresql:// ou postgres://
    POSTGRES_URL=${POSTGRES_URL#postgresql://}
    POSTGRES_URL=${POSTGRES_URL#postgres://}
    
    # Extrair username:password@host:port/database?params
    if [[ $POSTGRES_URL == *@* ]]; then
        AUTH_PART=${POSTGRES_URL%%@*}
        REST=${POSTGRES_URL#*@}
        
        if [[ $AUTH_PART == *:* ]]; then
            POSTGRES_USER=${AUTH_PART%%:*}
            POSTGRES_PASSWORD=${AUTH_PART#*:}
        else
            POSTGRES_USER=$AUTH_PART
            POSTGRES_PASSWORD=""
        fi
    else
        POSTGRES_USER=""
        POSTGRES_PASSWORD=""
        REST=$POSTGRES_URL
    fi
    
    # Extrair host:port/database
    if [[ $REST == */* ]]; then
        HOST_PORT=${REST%%/*}
        DB_PART=${REST#*/}
        
        # Remover query params do database
        DB_PART=${DB_PART%%\?*}
        POSTGRES_DATABASE=$DB_PART
    else
        HOST_PORT=$REST
        POSTGRES_DATABASE=""
    fi
    
    if [[ $HOST_PORT == *:* ]]; then
        POSTGRES_HOST=${HOST_PORT%%:*}
        POSTGRES_PORT=${HOST_PORT#*:}
    else
        POSTGRES_HOST=$HOST_PORT
        POSTGRES_PORT="5432"
    fi
    
    echo "# PostgreSQL (Neon)"
    echo "POLAR_POSTGRES_USER: ${POSTGRES_USER}"
    echo "POLAR_POSTGRES_PWD: ${POSTGRES_PASSWORD}"
    echo "POLAR_POSTGRES_HOST: ${POSTGRES_HOST}"
    echo "POLAR_POSTGRES_PORT: \"${POSTGRES_PORT}\""
    echo "POLAR_POSTGRES_DATABASE: ${POSTGRES_DATABASE}"
    echo ""
fi

echo -e "${GREEN}=== Copie as variáveis acima para o arquivo env.yaml ===${NC}"

