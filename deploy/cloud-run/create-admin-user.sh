#!/bin/bash
# Script para criar usuário admin usando variáveis de ambiente de produção

set -e

cd "$(dirname "$0")/../../server"

# Carregar variáveis de ambiente do arquivo env.yaml
# Convertendo formato YAML para export
while IFS=': ' read -r key value; do
  # Ignorar linhas vazias, comentários e linhas sem ':'
  [[ -z "$key" || "$key" =~ ^# ]] && continue
  [[ ! "$key" =~ : ]] && continue
  
  # Remover espaços e aspas
  key=$(echo "$key" | tr -d ' ')
  value=$(echo "$value" | tr -d ' "' | tr -d "'")
  
  # Exportar variável
  export "$key=$value"
done < ../env.yaml

# Executar o script Python
uv run python -m scripts.create_admin_user <EMAIL>

