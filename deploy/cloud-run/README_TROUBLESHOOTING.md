# Troubleshooting: Worker de Email

## 🚨 Problema: Emails não estão sendo enviados

### Diagnóstico Rápido

```bash
# 1. Verificar se há tarefas na fila
./test-redis-queue.sh

# 2. Verificar status do worker
gcloud run services describe fluu-worker --region us-east1 \
  --format="get(status.conditions[0].status)"

# 3. Executar diagnóstico completo
./diagnose-worker.sh
```

### Solução Rápida

Se o worker não está processando tarefas:

1. **Verificar formato das filas** (problema mais comum):
   ```bash
   # O script deve converter vírgulas em espaços
   grep "tr ',' ' '" server/start-worker.sh
   ```

2. **Rebuild da imagem Docker** (se necessário):
   ```bash
   cd server
   docker build -t gcr.io/pix-api-proxy-1758593444/polar-api:latest -f Dockerfile .
   docker push gcr.io/pix-api-proxy-1758593444/polar-api:latest
   ```

3. **Redeploy do worker**:
   ```bash
   ./deploy-worker.sh
   ```

## 📚 Documentação Completa

- **[Guia de Resolução](./GUIA_RESOLUCAO_WORKER_EMAIL.md)** - Passo a passo detalhado
- **[Prompt para Agente IA](./PROMPT_AGENTE_IA_WORKER_EMAIL.md)** - Para resolver com IA
- **[Prevenção](./PREVENCAO_PROBLEMAS_WORKER.md)** - Como evitar problemas

## 🔧 Scripts Úteis

- `diagnose-worker.sh` - Diagnóstico completo
- `test-redis-queue.sh` - Testar conexão Redis e filas
- `deploy-worker.sh` - Deploy com validações

## 📞 Suporte

Se o problema persistir:
1. Execute `./diagnose-worker.sh`
2. Colete logs: `gcloud run services logs read fluu-worker --region us-east1 --limit 100`
3. Consulte a documentação completa

