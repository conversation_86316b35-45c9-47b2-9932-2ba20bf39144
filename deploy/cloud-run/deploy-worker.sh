#!/bin/bash
set -e

# Configurações
PROJECT_ID="pix-api-proxy-1758593444"
REGION="us-east1"
SERVICE_NAME="fluu-worker"
IMAGE_NAME="gcr.io/${PROJECT_ID}/polar-api"  # Usa a mesma imagem do backend

echo "🚀 Iniciando deploy do worker..."

# Verificar se estamos no diretório correto
if [ ! -f "server/Dockerfile" ]; then
    echo "❌ Erro: Execute este script a partir da raiz do projeto"
    exit 1
fi

# Verificar se env-worker.yaml existe
if [ ! -f "deploy/cloud-run/env-worker.yaml" ]; then
    echo "❌ Erro: arquivo env-worker.yaml não encontrado"
    exit 1
fi

# Validações preventivas
echo "🔍 Validando configuração do worker..."

# Verificar se script start-worker.sh converte vírgulas em espaços
if ! grep -q "tr ',' ' '" server/start-worker.sh; then
    echo "❌ ERRO CRÍTICO: Script start-worker.sh não converte vírgulas em espaços!"
    echo "   O Dramatiq espera filas separadas por ESPAÇO, não vírgula."
    echo "   Adicione esta linha no script:"
    echo "   QUEUES=\$(echo \${DRAMATIQ_QUEUES:-high_priority,default} | tr ',' ' ')"
    exit 1
fi
echo "   ✅ Script start-worker.sh converte vírgulas corretamente"

# Verificar variáveis críticas no env-worker.yaml
if ! grep -q "POLAR_EMAIL_SENDER:" deploy/cloud-run/env-worker.yaml; then
    echo "⚠️  AVISO: POLAR_EMAIL_SENDER não encontrado em env-worker.yaml"
fi

if ! grep -q "POLAR_RESEND_API_KEY:" deploy/cloud-run/env-worker.yaml; then
    echo "⚠️  AVISO: POLAR_RESEND_API_KEY não encontrado em env-worker.yaml"
fi

if ! grep -q "DRAMATIQ_QUEUES:" deploy/cloud-run/env-worker.yaml; then
    echo "⚠️  AVISO: DRAMATIQ_QUEUES não encontrado em env-worker.yaml"
fi

echo "✅ Validação concluída"

# Verificar se a imagem do backend já existe (podemos reutilizar)
echo "ℹ️  Worker usa a mesma imagem do backend (polar-api)"
echo "   Se a imagem não existir, você precisa fazer deploy do backend primeiro"

# Deploy no Cloud Run
echo "🚀 Deploying worker to Cloud Run..."
cd deploy/cloud-run

# Usar --set-env-vars para definir dramatiq_prom_port dinamicamente
# O Cloud Run injeta PORT automaticamente, então usamos ele
gcloud run deploy ${SERVICE_NAME} \
  --image ${IMAGE_NAME}:latest \
  --region ${REGION} \
  --platform managed \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --min-instances 1 \
  --max-instances 5 \
  --timeout 3600 \
  --concurrency 1 \
  --env-vars-file=env-worker.yaml \
  --port 8080 \
  --cpu-boost \
  --command="/app/server/start-worker.sh"

if [ $? -ne 0 ]; then
    echo "❌ Erro no deploy"
    exit 1
fi

# Obter URL do serviço (se houver health check)
WORKER_URL=$(gcloud run services describe ${SERVICE_NAME} \
  --region ${REGION} \
  --format="get(status.url)" 2>/dev/null || echo "")

echo ""
echo "✅ Worker deployado com sucesso!"
if [ -n "$WORKER_URL" ]; then
    echo "📍 URL: ${WORKER_URL}"
    echo "🧪 Teste o health check:"
    echo "   curl ${WORKER_URL}/"
fi
echo ""
echo "📊 Ver logs do worker:"
echo "   gcloud run services logs read ${SERVICE_NAME} --region ${REGION}"
echo ""
