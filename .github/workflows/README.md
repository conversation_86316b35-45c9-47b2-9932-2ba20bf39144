# 🚀 Workflows Google Cloud Run

Workflows simplificados para deploy no Google Cloud Run.

## 📋 Workflows Disponíveis

### 1. **Build and Push to GCR** (`build-and-push.yml`)
**Execução:** Automática no push para `main`

**O que faz:**
- ✅ Detecta mudanças em `server/` ou `clients/`
- ✅ Faz build das imagens Docker
- ✅ Faz push para Google Container Registry (GCR)

**Não faz deploy!** Apenas build e push.

---

### 2. **Deploy Backend** (`deploy-backend.yml`)
**Execução:** Manual via GitHub Actions

**Como usar:**
1. Vá em **Actions** → **Deploy Backend to Cloud Run**
2. Clique em **Run workflow**
3. Selecione:
   - **Deploy to Cloud Run?** → `true`
   - **Image tag** → `latest` ou SHA específico
4. Clique em **Run workflow**

---

### 3. **Deploy Worker** (`deploy-worker.yml`)
**Execução:** Manual via GitHub Actions

**Como usar:**
1. Vá em **Actions** → **Deploy Worker to Cloud Run**
2. Clique em **Run workflow**
3. Selecione:
   - **Deploy to Cloud Run?** → `true`
   - **Image tag** → `latest` ou SHA específico
4. Clique em **Run workflow**

**Nota:** Worker usa a mesma imagem do backend (`polar-api`)

---

### 4. **Deploy Frontend** (`deploy-frontend.yml`)
**Execução:** Manual via GitHub Actions

**Como usar:**
1. Vá em **Actions** → **Deploy Frontend to Cloud Run**
2. Clique em **Run workflow**
3. Selecione:
   - **Deploy to Cloud Run?** → `true`
   - **Image tag** → `latest` ou SHA específico
4. Clique em **Run workflow**

---

## 🔄 Fluxo de Trabalho

```
1. Push para main
   └─► Build and Push (automático)
       └─► Imagens prontas no GCR

2. Deploy Manual (quando quiser)
   ├─► Deploy Backend
   ├─► Deploy Worker
   └─► Deploy Frontend
```

---

## ✅ Checklist

- [ ] Build and Push executou com sucesso
- [ ] Imagens estão no GCR
- [ ] Execute deploy manual quando necessário
- [ ] Verifique URL do serviço após deploy

---

## 🔗 Links Úteis

- **Google Cloud Console:** https://console.cloud.google.com
- **Container Registry:** https://console.cloud.google.com/gcr
- **Cloud Run:** https://console.cloud.google.com/run

