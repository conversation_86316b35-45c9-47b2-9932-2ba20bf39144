name: Deploy Backend to Cloud Run

on:
  workflow_run:
    workflows: ["Build and Push to GCR"]
    types:
      - completed
    branches:
      - main
  workflow_dispatch:
    inputs:
      deploy:
        description: 'Deploy to Cloud Run?'
        required: true
        default: 'true'
        type: choice
        options:
          - 'true'
          - 'false'
      image_tag:
        description: 'Image tag (leave empty for latest)'
        required: false
        type: string
        default: 'latest'

env:
  PROJECT_ID: pix-api-proxy-1758593444
  REGION: us-east1
  SERVICE_NAME: fluu-api
  IMAGE_NAME: gcr.io/pix-api-proxy-1758593444/polar-api

jobs:
  deploy:
    name: Deploy Backend
    runs-on: ubuntu-latest
    # Executa se: build foi bem-sucedido E houve mudanças no backend OU execução manual com deploy=true
    if: |
      (github.event.workflow_run.conclusion == 'success' && github.event.workflow_run.event == 'push') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.deploy == 'true')
    timeout-minutes: 10

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔐 Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: ☁️ Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: 🚀 Deploy to Cloud Run
        run: |
          # Usar latest se for execução automática, ou tag do input se for manual
          if [ "${{ github.event_name }}" == "workflow_run" ]; then
            IMAGE_TAG="latest"
          else
            IMAGE_TAG="${{ github.event.inputs.image_tag }}"
            if [ -z "$IMAGE_TAG" ]; then
              IMAGE_TAG="latest"
            fi
          fi
          
          gcloud run deploy ${{ env.SERVICE_NAME }} \
            --image ${{ env.IMAGE_NAME }}:${IMAGE_TAG} \
            --region ${{ env.REGION }} \
            --platform managed \
            --allow-unauthenticated \
            --memory 2Gi \
            --cpu 2 \
            --min-instances 1 \
            --max-instances 10 \
            --timeout 300 \
            --concurrency 80 \
            --env-vars-file=deploy/cloud-run/env.yaml \
            --quiet

      - name: 📍 Get service URL
        id: get-url
        run: |
          SERVICE_URL=$(gcloud run services describe ${{ env.SERVICE_NAME }} \
            --region ${{ env.REGION }} \
            --format="get(status.url)")
          echo "url=$SERVICE_URL" >> $GITHUB_OUTPUT
          echo "✅ Backend deployed: $SERVICE_URL"

      - name: 🌐 Configure Custom Domain
        run: |
          # Verifica se o domain mapping já existe
          if ! gcloud beta run domain-mappings describe api.fluu.digital --region ${{ env.REGION }} &>/dev/null; then
            echo "📝 Creating domain mapping for api.fluu.digital..."
            gcloud beta run domain-mappings create \
              --service ${{ env.SERVICE_NAME }} \
              --domain api.fluu.digital \
              --region ${{ env.REGION }} || echo "⚠️ Domain mapping may already exist or require manual DNS configuration"
          else
            echo "✅ Domain mapping already exists"
          fi
          
          # Mostra informações do domain mapping
          echo "📋 Domain mapping status:"
          gcloud beta run domain-mappings describe api.fluu.digital --region ${{ env.REGION }} --format="yaml(status)" || true

      - name: ✅ Deployment Summary
        run: |
          echo "## 🎉 Backend Deployment Successful" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Service:** \`${{ env.SERVICE_NAME }}\`" >> $GITHUB_STEP_SUMMARY
          echo "**Region:** \`${{ env.REGION }}\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Cloud Run URL:** ${{ steps.get-url.outputs.url }}" >> $GITHUB_STEP_SUMMARY
          echo "**Custom Domain:** https://api.fluu.digital" >> $GITHUB_STEP_SUMMARY

