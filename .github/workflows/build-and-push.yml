name: Build and Push to GCR

on:
  push:
    branches:
      - main
  workflow_dispatch:

env:
  PROJECT_ID: pix-api-proxy-1758593444
  GCR_REGISTRY: gcr.io

jobs:
  detect-changes:
    name: Detect Changes
    runs-on: ubuntu-latest
    outputs:
      backend: ${{ steps.filter.outputs.backend }}
      frontend: ${{ steps.filter.outputs.frontend }}
    steps:
      - uses: actions/checkout@v5
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            backend:
              - 'server/**'
            frontend:
              - 'clients/**'

  build-backend:
    name: Build Backend Image
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.backend == 'true' || github.event_name == 'workflow_dispatch'
    timeout-minutes: 20
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔐 Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: ☁️ Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔑 Configure Docker for GCR
        run: gcloud auth configure-docker --quiet

      - name: 🏗️ Build Backend Docker Image
        run: |
          IMAGE_NAME="${GCR_REGISTRY}/${PROJECT_ID}/polar-api"
          docker build \
            --platform linux/amd64 \
            -f server/Dockerfile \
            -t ${IMAGE_NAME}:${{ github.sha }} \
            -t ${IMAGE_NAME}:latest \
            ./server

      - name: 📤 Push Backend Image to GCR
        run: |
          IMAGE_NAME="${GCR_REGISTRY}/${PROJECT_ID}/polar-api"
          docker push ${IMAGE_NAME}:${{ github.sha }}
          docker push ${IMAGE_NAME}:latest
          echo "✅ Backend image pushed: ${IMAGE_NAME}:${{ github.sha }}"

  build-frontend:
    name: Build Frontend Image
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.frontend == 'true' || github.event_name == 'workflow_dispatch'
    timeout-minutes: 30
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔐 Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: ☁️ Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔑 Configure Docker for GCR
        run: gcloud auth configure-docker --quiet

      - name: 📋 Extract NEXT_PUBLIC_* variables
        id: extract-env-vars
        run: |
          BUILD_ARGS_FILE=$(mktemp)
          while IFS= read -r line || [ -n "$line" ]; do
            if [[ -z "$line" ]] || [[ "$line" =~ ^[[:space:]]*# ]] || [[ "$line" =~ ^[[:space:]]*=== ]]; then
              continue
            fi
            if [[ "$line" =~ ^[[:space:]]*([A-Z_]+):[[:space:]]*(.+)$ ]]; then
              VAR_NAME="${BASH_REMATCH[1]}"
              VAR_VALUE="${BASH_REMATCH[2]}"
              VAR_VALUE=$(echo "$VAR_VALUE" | sed -e 's/^"//' -e 's/"$//' -e "s/^'//" -e "s/'$//")
              if [[ "$VAR_NAME" =~ ^NEXT_PUBLIC_ ]]; then
                echo "--build-arg ${VAR_NAME}=${VAR_VALUE}" >> "$BUILD_ARGS_FILE"
              fi
            fi
          done < deploy/cloud-run/env-frontend.yaml
          
          if [ -s "$BUILD_ARGS_FILE" ]; then
            BUILD_ARGS=$(cat "$BUILD_ARGS_FILE")
            echo "build_args<<EOF" >> $GITHUB_OUTPUT
            echo "$BUILD_ARGS" >> $GITHUB_OUTPUT
            echo "EOF" >> $GITHUB_OUTPUT
          else
            echo "build_args=" >> $GITHUB_OUTPUT
          fi
          rm -f "$BUILD_ARGS_FILE"

      - name: 🏗️ Build Frontend Docker Image
        run: |
          IMAGE_NAME="${GCR_REGISTRY}/${PROJECT_ID}/polar-frontend"
          BUILD_ARGS="${{ steps.extract-env-vars.outputs.build_args }}"
          
          echo "🔍 Validando variáveis de build..."
          if [ -n "$BUILD_ARGS" ]; then
            echo "Build args extraídos:"
            echo "$BUILD_ARGS" | tr ' ' '\n' | grep -E "NEXT_PUBLIC" || echo "⚠️  Nenhuma variável NEXT_PUBLIC encontrada"
          else
            echo "⚠️  AVISO: Nenhum build arg extraído. Verifique env-frontend.yaml"
          fi
          
          echo ""
          echo "🚀 Iniciando build da imagem Docker..."
          docker build \
            --progress=plain \
            --platform linux/amd64 \
            -f clients/Dockerfile \
            -t ${IMAGE_NAME}:${{ github.sha }} \
            -t ${IMAGE_NAME}:latest \
            $BUILD_ARGS \
            . 2>&1 | tee /tmp/docker-build.log
          
          # Verificar se o build foi bem-sucedido
          if [ ${PIPESTATUS[0]} -ne 0 ]; then
            echo "❌ ERRO: Build da imagem Docker falhou!"
            echo "Últimas 50 linhas do log:"
            tail -50 /tmp/docker-build.log
            exit 1
          fi
          
          echo "✅ Build da imagem concluído com sucesso"
          
          # Verificar se o .next foi gerado (inspecionar a imagem)
          echo ""
          echo "🔍 Verificando se o diretório .next foi gerado..."
          docker run --rm --entrypoint sh ${IMAGE_NAME}:latest -c \
            "ls -la /app/clients/apps/web/.next 2>/dev/null || echo '❌ ERRO: Diretório .next não encontrado na imagem!'" || {
            echo "❌ ERRO: Não foi possível verificar o diretório .next na imagem"
            echo "Isso pode indicar que o build do Next.js falhou silenciosamente"
            exit 1
          }

      - name: 📤 Push Frontend Image to GCR
        run: |
          IMAGE_NAME="${GCR_REGISTRY}/${PROJECT_ID}/polar-frontend"
          docker push ${IMAGE_NAME}:${{ github.sha }}
          docker push ${IMAGE_NAME}:latest
          echo "✅ Frontend image pushed: ${IMAGE_NAME}:${{ github.sha }}"

