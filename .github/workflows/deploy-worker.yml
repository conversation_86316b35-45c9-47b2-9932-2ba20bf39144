name: Deploy Worker to Cloud Run

on:
  workflow_run:
    workflows: ["Build and Push to GCR"]
    types:
      - completed
    branches:
      - main
  workflow_dispatch:
    inputs:
      deploy:
        description: 'Deploy to Cloud Run?'
        required: true
        default: 'true'
        type: choice
        options:
          - 'true'
          - 'false'
      image_tag:
        description: 'Image tag (leave empty for latest)'
        required: false
        type: string
        default: 'latest'

env:
  PROJECT_ID: pix-api-proxy-1758593444
  REGION: us-east1
  SERVICE_NAME: fluu-worker
  IMAGE_NAME: gcr.io/pix-api-proxy-1758593444/polar-api  # Worker usa mesma imagem do backend

jobs:
  deploy:
    name: Deploy Worker
    runs-on: ubuntu-latest
    # Executa se: build foi bem-sucedido E houve mudanças no backend OU execução manual com deploy=true
    if: |
      (github.event.workflow_run.conclusion == 'success' && github.event.workflow_run.event == 'push') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.deploy == 'true')
    timeout-minutes: 10

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔐 Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: ☁️ Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: 🔍 Validate Worker Configuration
        run: |
          echo "🔍 Validando configuração do worker..."
          
          # Verificar se script start-worker.sh converte vírgulas em espaços
          if ! grep -q "tr ',' ' '" server/start-worker.sh; then
            echo "❌ ERRO CRÍTICO: Script start-worker.sh não converte vírgulas em espaços!"
            echo "   O Dramatiq espera filas separadas por ESPAÇO, não vírgula."
            exit 1
          fi
          echo "   ✅ Script start-worker.sh converte vírgulas corretamente"
          
          # Verificar variáveis críticas
          if [ ! -f "deploy/cloud-run/env-worker.yaml" ]; then
            echo "❌ ERRO: env-worker.yaml não encontrado"
            exit 1
          fi
          
          if ! grep -q "POLAR_EMAIL_SENDER:" deploy/cloud-run/env-worker.yaml; then
            echo "⚠️  AVISO: POLAR_EMAIL_SENDER não encontrado"
          fi
          
          if ! grep -q "POLAR_RESEND_API_KEY:" deploy/cloud-run/env-worker.yaml; then
            echo "⚠️  AVISO: POLAR_RESEND_API_KEY não encontrado"
          fi
          
          if ! grep -q "DRAMATIQ_QUEUES:" deploy/cloud-run/env-worker.yaml; then
            echo "⚠️  AVISO: DRAMATIQ_QUEUES não encontrado"
          fi
          
          echo "✅ Validação concluída"

      - name: 🚀 Deploy to Cloud Run
        run: |
          # Usar latest se for execução automática, ou tag do input se for manual
          if [ "${{ github.event_name }}" == "workflow_run" ]; then
            IMAGE_TAG="latest"
          else
            IMAGE_TAG="${{ github.event.inputs.image_tag }}"
            if [ -z "$IMAGE_TAG" ]; then
              IMAGE_TAG="latest"
            fi
          fi
          
          echo "🚀 Deploying worker with image: ${{ env.IMAGE_NAME }}:${IMAGE_TAG}"
          
          gcloud run deploy ${{ env.SERVICE_NAME }} \
            --image ${{ env.IMAGE_NAME }}:${IMAGE_TAG} \
            --region ${{ env.REGION }} \
            --platform managed \
            --allow-unauthenticated \
            --memory 2Gi \
            --cpu 2 \
            --min-instances 1 \
            --max-instances 5 \
            --timeout 3600 \
            --concurrency 1 \
            --env-vars-file=deploy/cloud-run/env-worker.yaml \
            --command="/app/server/start-worker.sh" \
            --port 8080 \
            --cpu-boost \
            --quiet

      - name: 📍 Get service URL
        id: get-url
        run: |
          SERVICE_URL=$(gcloud run services describe ${{ env.SERVICE_NAME }} \
            --region ${{ env.REGION }} \
            --format="get(status.url)")
          echo "url=$SERVICE_URL" >> $GITHUB_OUTPUT
          echo "✅ Worker deployed: $SERVICE_URL"

      - name: ✅ Deployment Summary
        run: |
          echo "## 🎉 Worker Deployment Successful" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Service:** \`${{ env.SERVICE_NAME }}\`" >> $GITHUB_STEP_SUMMARY
          echo "**Region:** \`${{ env.REGION }}\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Cloud Run URL:** ${{ steps.get-url.outputs.url }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "> ⚠️ Worker is not publicly accessible (no custom domain)" >> $GITHUB_STEP_SUMMARY

