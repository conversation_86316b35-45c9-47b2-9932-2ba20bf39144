name: Deploy Frontend to Cloud Run

on:
  workflow_run:
    workflows: ["Build and Push to GCR"]
    types:
      - completed
    branches:
      - main
  workflow_dispatch:
    inputs:
      deploy:
        description: 'Deploy to Cloud Run?'
        required: true
        default: 'true'
        type: choice
        options:
          - 'true'
          - 'false'
      image_tag:
        description: 'Image tag (leave empty for latest)'
        required: false
        type: string
        default: 'latest'

env:
  PROJECT_ID: pix-api-proxy-1758593444
  REGION: us-east1
  SERVICE_NAME: fluu-web
  IMAGE_NAME: gcr.io/pix-api-proxy-1758593444/polar-frontend

jobs:
  deploy:
    name: Deploy Frontend
    runs-on: ubuntu-latest
    # Executa se: build foi bem-sucedido E houve mudanças no frontend OU execução manual com deploy=true
    if: |
      (github.event.workflow_run.conclusion == 'success' && github.event.workflow_run.event == 'push') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.deploy == 'true')
    timeout-minutes: 10

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: 🔍 Check if frontend changed
        id: check-frontend
        if: github.event_name == 'workflow_run'
        run: |
          # Verificar se o build do frontend foi executado (houve mudanças em clients/)
          if [ "${{ github.event.workflow_run.conclusion }}" == "success" ]; then
            echo "frontend_changed=true" >> $GITHUB_OUTPUT
          else
            echo "frontend_changed=false" >> $GITHUB_OUTPUT
          fi

      - name: 🔐 Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: ☁️ Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔑 Configure Docker for GCR
        run: gcloud auth configure-docker --quiet


      - name: 🚀 Deploy to Cloud Run
        run: |
          # Usar latest se for execução automática, ou tag do input se for manual
          if [ "${{ github.event_name }}" == "workflow_run" ]; then
            IMAGE_TAG="latest"
          else
            IMAGE_TAG="${{ github.event.inputs.image_tag }}"
            if [ -z "$IMAGE_TAG" ]; then
              IMAGE_TAG="latest"
            fi
          fi
          
          gcloud run deploy ${{ env.SERVICE_NAME }} \
            --image ${{ env.IMAGE_NAME }}:${IMAGE_TAG} \
            --region ${{ env.REGION }} \
            --platform managed \
            --allow-unauthenticated \
            --memory 2Gi \
            --cpu 2 \
            --min-instances 0 \
            --max-instances 10 \
            --timeout 600 \
            --concurrency 80 \
            --env-vars-file=deploy/cloud-run/env-frontend.yaml \
            --port 8080 \
            --cpu-boost \
            --quiet

      - name: 📍 Get service URL
        id: get-url
        run: |
          SERVICE_URL=$(gcloud run services describe ${{ env.SERVICE_NAME }} \
            --region ${{ env.REGION }} \
            --format="get(status.url)")
          echo "url=$SERVICE_URL" >> $GITHUB_OUTPUT
          echo "✅ Frontend deployed successfully!"
          echo "📍 URL: $SERVICE_URL"

      - name: ✅ Deployment Summary
        run: |
          echo "## 🎉 Frontend Deployment Successful" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Service:** \`${{ env.SERVICE_NAME }}\`" >> $GITHUB_STEP_SUMMARY
          echo "**Region:** \`${{ env.REGION }}\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Cloud Run URL:** ${{ steps.get-url.outputs.url }}" >> $GITHUB_STEP_SUMMARY
          echo "**Custom Domain:** https://fluu.digital" >> $GITHUB_STEP_SUMMARY

